[{"title": "工作台", "icon": {"name": "my<PERSON><PERSON>:table-solid", "source": "iconify"}, "path": "/to_do", "tabs": [{"title": "看板", "path": "/to_do"}, {"title": "团队任务", "path": "/workbench/team"}, {"title": "个人任务", "path": "/workbench/person"}]}, {"title": "项目管理", "icon": {"name": "material-symbols:checklist", "source": "iconify"}, "path": "/projects", "children": [{"title": "项目信息", "path": "/projects", "tabs": [{"title": "项目信息", "path": "/projects"}]}, {"title": "需求管理", "path": "/requirements", "tabs": [{"title": "项目需求", "path": "/requirements"}]}, {"title": "文档管理", "path": "/to_do"}, {"title": "产品类型", "path": "/product_types"}]}, {"title": "研发管理", "icon": {"name": "material-symbols:checklist", "source": "iconify"}, "path": "/projects_repository", "children": [{"title": "代码仓库", "path": "/projects_repository", "tabs": [{"title": "代码仓库", "path": "/projects_repository"}]}, {"title": "开发计划", "path": "/to_do"}, {"title": "代码配置", "path": "/code_management/gpio", "tabs": [{"title": "芯片配置", "path": "/code_management/gpio"}, {"title": "功能模块", "path": "/code_management/config"}]}, {"title": "提测记录", "path": "/test_submission"}]}, {"title": "测试管理", "path": "/test_cases2", "icon": {"name": "3-1", "source": ""}, "children": [{"title": "用例管理", "path": "/test_cases2", "tabs": [{"title": "测试用例", "path": "/test_cases2"}, {"title": "测试计划", "path": "/test_plans_v2"}, {"title": "测试记录", "path": "/test_records_v2"}, {"title": "故障记录", "path": "/process_monitor"}, {"title": "测试产品", "path": "/test_products"}]}, {"title": "版本管理", "path": "/test_reports_v2", "tabs": [{"title": "测试报告", "path": "/test_reports_v2"}, {"title": "版本管理", "path": "/product_versions"}]}, {"title": "压测管理", "path": "/stress_tests", "tabs": [{"title": "压测记录", "path": "/stress_tests"}]}, {"title": "机台管理", "path": "/machines", "tabs": [{"title": "机台排期", "path": "/machine_calendar"}, {"title": "机台列表", "path": "/machines"}, {"title": "预约记录", "path": "/machines/reservations"}, {"title": "设备类型", "path": "/device_types/list"}, {"title": "设备列表", "path": "/devices/list"}, {"title": "机台功能", "path": "/functions"}]}, {"title": "功能模块", "path": "/functions2", "tabs": [{"title": "模块列表", "path": "/functions2"}]}, {"title": "公共用例", "path": "/public_test_cases/list", "tabs": [{"title": "海微标准", "path": "/public_test_cases/list"}, {"title": "NIO标准", "path": "/es_test_cases/nio_list"}, {"title": "岚图标准", "path": "/es_test_cases/voyah_list"}, {"title": "用例活动类型", "path": "/test_cases/types"}, {"title": "用例标签", "path": "/test_case_tags"}]}, {"title": "样件管理", "path": "/test_prototypes"}, {"title": "图片资源", "path": "/image_resources"}, {"title": "自动化指令", "path": "/custom_cmds", "tabs": [{"title": "自定义指令配置", "path": "/custom_cmds"}]}, {"title": "公共点检项", "path": "/inspection_items"}]}, {"title": "实验管理", "path": "/am_devices", "icon": {"name": "mdi:account-box-multiple", "source": "iconify"}, "tabs": [{"title": "实验控制", "path": "/am_devices"}, {"title": "实验工位", "path": "/am_stations"}]}, {"title": "CICD", "icon": {"name": "tabler:brand-github", "source": "iconify"}, "children": [{"title": "GRPC节点", "path": "/to_do", "tabs": [{"title": "GRPC节点", "path": "/to_do"}]}, {"title": "流程管理", "path": "/cicd", "tabs": [{"title": "流程管理", "path": "/cicd"}]}, {"title": "执行记录", "path": "/to_do", "tabs": [{"title": "执行记录", "path": "/to_do"}]}]}, {"title": "辅助工具", "path": "/pack_tools/records", "icon": {"name": "fluent:window-dev-tools-16-filled", "source": "iconify"}, "children": [{"title": "打包工具", "path": "/pack_tools/records", "tabs": [{"title": "打包工具", "path": "/pack_tools/records"}, {"title": "差分工具", "path": "/software_tools/diff_tool_records"}]}, {"title": "软件管理", "path": "/programs/versions", "tabs": [{"title": "程序类型", "path": "/programs"}, {"title": "程序版本", "path": "/programs/versions"}]}]}]