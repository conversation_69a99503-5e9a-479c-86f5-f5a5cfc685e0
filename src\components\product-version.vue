<template>
    <el-tree-select v-if="productVersionV" ref="productVersionRef" v-model="model" lazy :load="loadVersions" multiple
        clearable :data="projectVersionData" :cache-data="props.initData"
        :props="{ label: 'label', value: 'value', children: 'children', isLeaf: 'isLeaf' }">
    </el-tree-select>

</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue';
import http from '@/utils/http/http.js';

const props = defineProps({
    projectNumber: {
        type: String,
        default: ''
    },
    exclude: {
        type: Array,
        default: () => []
    },
    include: {
        type: Array,
        default: () => []
    },
    initData: {
        type: Array,
        default: () => []
    }
})
const model = defineModel('model', {
    type: Array,
    default: () => []
});

const productVersionV = ref(true);
const productVersionRef = ref(null);
const projectVersionDataInit = ref([]);
const projectVersionData = ref([]);


function loadVersions(node, resolve) {
    if (node.level === 0) {
        return resolve(projectVersionDataInit.value);
    }
    if (node.level === 1) {
        http.get('/issues/project_version_number',
            { params: { type: node.data.code, project_number: props.projectNumber } }).then(res => {
                if (res.data.err_code != 0) {
                    resolve([{ label: '无数据', value: '无数据', isLeaf: true, disabled: true }]);
                    return;
                }
                let data = res.data.data.records;
                data = data.map(item => {
                    item.label = item.name;
                    item.value = item.id;
                    item.type_name = node.data.label;
                    item.isLeaf = true;
                    return item;
                });
                resolve(data);
                if (data.length == 0) {
                    resolve([{ label: '无数据', value: '无数据', isLeaf: true, disabled: true }]);
                }
            });
    }
}

watch(() => props.projectNumber, () => {
    model.value = [];
    projectVersionData.value = projectVersionDataInit.value;
    productVersionV.value = false;
    nextTick(() => {
        productVersionV.value = true;
    });
});


const getSelected = () => {
    let versions = model.value;

    versions = versions.map(item => {
        let i = props.initData.find(item2 => item2.value == item);
        if (i) {
            return i;
        }
        return productVersionRef.value.getNode(item)?.data;
    });

    return versions;
};


defineExpose({
    getSelected
});


onMounted(() => {
    http.get('/issues/project_version_type').then(res => {
        let data = res.data.data.dictList;

        data = data.filter(item => {
            if (props.include && props.include.length > 0) {
                return props.include.includes(item.code);
            }
            return true;
        });

        data = data.filter(item => {
            if (props.exclude && props.exclude.length > 0) {
                return !props.exclude.includes(item.code);
            }
            return true;
        });

        data = data.map(item => {
            item.value = item.id;
            item.isLeaf = false;
            return item;
        });
        projectVersionDataInit.value = data;
        projectVersionData.value = data;
    });
});

</script>

<style scoped></style>