<template>
  <div class="dynamic-pin-editor">
    <div class="card">
      <h2 class="card-title">
        <el-icon style="color: #ccc; font-size: 14px; margin-right: 5px;"><EditPen /></el-icon>
        引脚编辑器
        <el-tag v-if="isReadOnlyMode" type="warning" size="small" style="margin-left: 10px;">
          只读模式
        </el-tag>
      </h2>

      <!-- 只读模式提示 -->
      <el-alert
        v-if="isReadOnlyMode"
        title="此引脚为特殊类型，仅允许查看配置信息，不允许修改"
        type="info"
        :closable="false"
        style="margin-bottom: 15px;"
      />

      <el-form :model="formData" label-position="left" :label-width="dynamicLabelWidth" class="form-left-align">
        <!-- 基本信息 -->
        <div class="section-header">
          <el-divider content-position="left">
            <span class="section-title">
              <el-icon><InfoFilled /></el-icon>
              基本信息
            </span>
          </el-divider>
        </div>

        <el-form-item label="引脚编号">
          <el-input v-model="formData.pinId" disabled size="small"></el-input>
        </el-form-item>
        
        <el-form-item label="引脚名称">
          <el-input
            v-model="formData.pinName"
            :disabled="isReadOnlyMode"
            size="small"
            placeholder="请输入引脚名称"
            @input="handleFormChange"
            
          ></el-input>
        </el-form-item>
        
        <el-form-item label="功能类型">
          <el-select
            v-model="formData.pinType"
            :disabled="isReadOnlyMode"
            placeholder="请选择引脚类型"
            size="small"
            @change="handlePinTypeChange"
          >
            <el-option
              v-for="pinType in availablePinTypes"
              :key="pinType.value"
              :label="pinType.label"
              :value="pinType.value"
              :title="pinType.description"
            >
              <span style="float: left">{{ pinType.label }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ pinType.description }}</span>
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 动态生成的配置区域 -->
        <div v-if="currentTypeConfig && formData.pinType" class="dynamic-config-area">
          <!-- 基本配置字段 -->
          <div v-if="currentBasicFields.length > 0" class="config-section">
            <!-- <div class="section-header">
              <el-divider content-position="left">
                <span class="section-title">
                  <el-icon><Edit /></el-icon>
                  {{ formData.pinType }} 基本配置
                </span>
              </el-divider>
            </div> -->
            
            <template v-for="field in currentBasicFields" :key="field.key">
              <el-form-item :label="field.display">
                <div class="field-with-hint">
                  <el-select
                    v-if="field.type === 'enum'"
                    v-model="formData.dynamicConfig[field.key]"
                    v-bind="getFieldProps(field)"
                    @change="handleFormChange"
                  >
                    <el-option
                      v-for="option in field.list"
                      :key="option"
                      :label="option"
                      :value="option"
                    />
                  </el-select>
                  <component
                    v-else
                    :is="getFieldComponent(field)"
                    v-model="formData.dynamicConfig[field.key]"
                    v-bind="getFieldProps(field)"
                    @change="handleFormChange"
                  />
                  <!-- 可编辑的attribute_config默认值 -->
                  <el-input
                    v-if="(field.key === 'name' || field.key === 'des') && getAttributeConfigHint(field.key)"
                    v-model="formData.dynamicConfig[`${field.key}_hint`]"
                    :placeholder="`默认: ${getAttributeConfigHint(field.key)}`"
                    size="small"
                    class="config-hint-input"
                    @change="handleFormChange"
                  />
                </div>
              </el-form-item>
            </template>
          </div>

          <!-- PinInfo配置字段 -->
          <div v-if="currentPinInfoFields.length > 0" class="config-section">
            <!-- <div class="section-header">
              <el-divider content-position="left">
                <span class="section-title">
                  <el-icon><Connection /></el-icon>
                  引脚信息配置
                </span>
              </el-divider>
            </div> -->
            
            <template v-for="pinInfo in currentPinInfoFields" :key="pinInfo.key">
              <div class="pin-info-group">
                <h4 class="pin-info-title">{{ pinInfo.label }} 配置</h4>
                <template v-for="(fieldConfig, fieldKey) in pinInfo.config" :key="fieldKey">
                  <el-form-item :label="fieldConfig.display">
                    <el-select
                      v-if="fieldConfig.type === 'enum'"
                      v-model="formData.dynamicConfig[`${pinInfo.key}_${fieldKey}`]"
                      v-bind="getFieldProps(fieldConfig)"
                      @change="handleFormChange"
                    >
                      <el-option
                        v-for="option in fieldConfig.list"
                        :key="option"
                        :label="option"
                        :value="option"
                      />
                    </el-select>
                    <component
                      v-else
                      :is="getFieldComponent(fieldConfig)"
                      v-model="formData.dynamicConfig[`${pinInfo.key}_${fieldKey}`]"
                      v-bind="getFieldProps(fieldConfig)"
                      @change="handleFormChange"
                    />
                  </el-form-item>
                </template>
              </div>
            </template>
          </div>

          <!-- 配置参数字段 -->
          <div v-if="currentConfigFields.length > 0" class="config-section">
            <!-- <div class="section-header">
              <el-divider content-position="left">
                <span class="section-title">
                  <el-icon><Tools /></el-icon>
                  {{ formData.pinType }} 参数配置
                </span>
              </el-divider>
            </div> -->
            
            <template v-for="field in currentConfigFields" :key="field.key">
              <!-- 🔧 只有当字段有值时才显示，避免显示空白的label字段 -->
              <el-form-item
                v-if="shouldShowField(field)"
                :label="field.display"
              >
                <!-- 简化的渲染逻辑 -->
                <el-input
                  v-if="field.type === 'uint8_hex'"
                  v-model="formData.dynamicConfig[field.key]"
                  size="small"
                  placeholder="如: 0x2A 或 42"
                  @change="handleFormChange"
                />
                <el-input-number
                  v-else-if="field.type && field.type.includes('uint')"
                  v-model="formData.dynamicConfig[field.key]"
                  :min="0"
                  :max="999999"
                  size="small"
                  @change="handleFormChange"
                />
                <el-select
                  v-else-if="field.type === 'enum'"
                  v-model="formData.dynamicConfig[field.key]"
                  size="small"
                  @change="handleFormChange"
                >
                  <el-option
                    v-for="option in field.list"
                    :key="option"
                    :label="option"
                    :value="option"
                  />
                </el-select>
                <el-input
                  v-else-if="field.type === 'label'"
                  v-model="formData.dynamicConfig[field.key]"
                  size="small"
                  readonly
                  @change="handleFormChange"
                />
                <el-input
                  v-else
                  v-model="formData.dynamicConfig[field.key]"
                  size="small"
                  @change="handleFormChange"
                />
              </el-form-item>
            </template>
          </div>


        </div>


      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue';
import {
  InfoFilled, Edit, Connection, Tools, EditPen
} from '@element-plus/icons-vue';

// 从主文件导入PinValue数据
import { PinValue } from './config-data.js';

// Props
const props = defineProps({
  // 当前编辑的引脚数据
  pinData: {
    type: Object,
    default: () => ({
      pinId: '',
      pinName: '',
      pinType: '',
      desc: '',
      status: '可用',
      electricalType: 'TTL'
    })
  },
  // 芯片配置信息
  chipConfig: {
    type: Object,
    default: () => ({
      chipModel: '',
      vccVoltage: 3.3,
      clockFreq: 160,
      ioConfig: []
    })
  },
  // 仓库信息
  repositoryInfo: {
    type: Object,
    default: () => ({
      gitlab: '',
      project_branch: '',
      workspace: '',
      branch_status: ''
    })
  },
  // 可用的IO类型
  availableIoTypes: {
    type: Array,
    default: () => []
  },
  // 引脚的功能列表（来自function_list）
  pinFunctionList: {
    type: Array,
    default: () => []
  },
  // 是否禁用编辑
  disabled: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits([
  'update:pinData',  // 更新引脚数据
  'save',           // 保存配置
  'reset',          // 重置配置
  'clear',          // 清空配置
  'change'          // 数据变化
]);

// 表单数据
const formData = reactive({
  pinId: '',
  pinName: '',
  pinType: '',
  desc: '',
  status: '可用',
  electricalType: 'TTL',
  dynamicConfig: {} // 动态配置字段
});

// 功能模块配置数据
const functionModuleConfig = reactive({
  gitlab: '',
  project_branch: '',
  workspace: '',
  branch_status: ''
});



// 原始数据备份（用于检测变化）
const originalData = ref({});

// 获取所有可用的引脚类型 - 基于引脚的function_list
const availablePinTypes = computed(() => {
  // 如果有传入的pinFunctionList，基于它来生成可选类型
  if (props.pinFunctionList && props.pinFunctionList.length > 0) {
    const types = [];
    const chipFunctions = PinValue.Config_Onboard.Chip_function;

    // 将PIN_MODE映射为类型名称
    const pinModeToTypeMap = {
      'PIN_MODE_GPIO': 'GPIO',
      'PIN_MODE_ADC_INPUT': 'ADC',
      'PIN_MODE_PWM_OUTPUT': 'PWM',
      'PIN_MODE_I2C_SCL': 'IIC',
      'PIN_MODE_I2C_SDA': 'IIC',
      'PIN_MODE_SPI_CS': 'SPI',
      'PIN_MODE_SPI_SCLK': 'SPI',
      'PIN_MODE_SPI_MOSI': 'SPI',
      'PIN_MODE_SPI_MISO': 'SPI',
      'PIN_MODE_UART_RX': 'UART',
      'PIN_MODE_UART_TX': 'UART',
      'PIN_MODE_EXINT_IN': 'EXIT',
      'PIN_MODE_OTHERS': 'Power'
    };

    // 收集该引脚支持的所有类型
    const supportedTypes = new Set();
    props.pinFunctionList.forEach(pinMode => {
      const typeName = pinModeToTypeMap[pinMode];
      if (typeName) {
        supportedTypes.add(typeName);
      }
    });

    // 为每个支持的类型查找配置
    chipFunctions.forEach(funcGroup => {
      Object.keys(funcGroup).forEach(key => {
        if (supportedTypes.has(key)) {
          types.push({
            value: key,
            label: key,
            config: funcGroup[key].attribute_define,
            description: `${key} 类型引脚配置`
          });
        }
      });
    });

    return types;
  }

  // 如果没有pinFunctionList，使用原有逻辑（显示所有类型）
  const chipFunctions = PinValue.Config_Onboard.Chip_function;
  const types = [];

  chipFunctions.forEach(funcGroup => {
    Object.keys(funcGroup).forEach(key => {
      types.push({
        value: key,
        label: key,
        config: funcGroup[key].attribute_define,
        description: `${key} 类型引脚配置`
      });
    });
  });

  return types;
});

// 获取当前引脚类型的配置定义
const currentTypeConfig = computed(() => {
  if (!formData.pinType) {
    return null;
  }

  // 在PinValue中查找对应的类型配置
  const chipFunctions = PinValue.Config_Onboard.Chip_function;

  for (let i = 0; i < chipFunctions.length; i++) {
    const funcGroup = chipFunctions[i];

    if (funcGroup[formData.pinType]) {
      return funcGroup[formData.pinType].attribute_define;
    }
  }

  return null;
});

// 获取当前引脚类型的基本配置字段
const currentBasicFields = computed(() => {
  if (!currentTypeConfig.value) return [];
  
  const fields = [];
  const attrDefine = currentTypeConfig.value;
  
  // 添加基本字段
  if (attrDefine.name) {
    fields.push({
      key: 'name',
      ...attrDefine.name
    });
  }
  
  if (attrDefine.des) {
    fields.push({
      key: 'des',
      ...attrDefine.des
    });
  }
  
  return fields;
});

// 获取当前引脚类型的PinInfo配置
const currentPinInfoFields = computed(() => {
  if (!currentTypeConfig.value || !currentTypeConfig.value.PinInfo) return [];

  const pinInfoFields = [];
  currentTypeConfig.value.PinInfo.forEach(pinInfo => {
    Object.keys(pinInfo).forEach(pinKey => {
      const pinConfig = pinInfo[pinKey];

      // 处理pinConfig中的字段，确保list字段是数组格式
      const processedConfig = {};
      Object.keys(pinConfig).forEach(fieldKey => {
        const fieldConfig = { ...pinConfig[fieldKey] };

        // 如果list是字符串，转换为只有一个元素的数组
        if (fieldConfig.list && typeof fieldConfig.list === 'string') {
          fieldConfig.list = [fieldConfig.list];
        }

        processedConfig[fieldKey] = fieldConfig;
      });

      pinInfoFields.push({
        key: pinKey,
        label: pinKey,
        config: processedConfig
      });
    });
  });

  return pinInfoFields;
});

// 获取当前引脚类型的配置字段
const currentConfigFields = computed(() => {
  if (!currentTypeConfig.value || !currentTypeConfig.value.config) {
    return [];
  }

  console.log('原始配置数据:', currentTypeConfig.value.config);

  const configFields = [];
  Object.keys(currentTypeConfig.value.config).forEach(configKey => {
    const configItem = currentTypeConfig.value.config[configKey];
    console.log(`配置字段 ${configKey}:`, configItem);
    configFields.push({
      key: configKey,
      ...configItem
    });
  });

  console.log('生成的配置字段:', configFields);
  return configFields;
});

// 判断是否为只读模式
const isReadOnlyMode = computed(() => {
  return props.disabled || formData.pinType === 'Power' || formData.pinType === 'OTHER';
});

// 检测是否有变化
const hasChanges = computed(() => {
  if (!originalData.value.pinId) return false;
  
  return Object.keys(formData).some(key => {
    return formData[key] !== originalData.value[key];
  });
});

// 根据字段类型获取对应的组件
const getFieldComponent = (field) => {
  if (field.type === 'enum') return 'el-select';
  if (field.type === 'string') return 'el-input';
  if (field.type === 'label') return 'el-input';
  if (field.type && field.type.includes('uint')) return 'el-input-number';
  return 'el-input';
};

// 🔧 判断字段是否应该显示（避免显示空白的label字段）
const shouldShowField = (field) => {
  const fieldValue = formData.dynamicConfig[field.key];

  // 对于label类型的字段，只有当有值时才显示
  if (field.type === 'label') {
    return fieldValue !== undefined && fieldValue !== null && fieldValue !== '';
  }

  // 其他类型的字段都显示
  return true;
};

// 🔧 统一设置label宽度，确保界面一致性
const dynamicLabelWidth = computed(() => {
  // 使用固定宽度确保所有label对齐一致
  // 根据常见的中文label长度设置合适的宽度
  return '140px';
});

// 根据字段类型获取组件属性
const getFieldProps = (field) => {
  // 只有在只读模式下才禁用字段，label类型在参数配置中应该是可编辑的
  const isDisabled = isReadOnlyMode.value;

  const fieldProps = {
    size: 'small',
    disabled: isDisabled
  };

  if (field.type === 'enum' && field.list) {
    // 对于枚举类型，不需要额外属性，选项在模板中处理
    fieldProps.placeholder = '请选择';
  } else if (field.type && field.type.includes('uint')) {
    fieldProps.min = field.min || 0;
    fieldProps.max = field.max || 999999;
  } else {
    fieldProps.placeholder = `请输入${field.display}`;
  }

  return fieldProps;
};

// 防止递归调用的标志
const isInternalUpdate = ref(false);
// 记录上次处理的引脚ID，用于检测真正的外部更新
const lastProcessedPinId = ref('');
// 引脚状态缓存，保存每个引脚的用户修改
const pinStateCache = ref(new Map());

// 保存当前引脚状态到缓存
const savePinState = (pinId) => {
  if (pinId && formData.dynamicConfig && Object.keys(formData.dynamicConfig).length > 0) {
    pinStateCache.value.set(pinId, {
      ...formData,
      dynamicConfig: { ...formData.dynamicConfig }
    });
  }
};

// 从缓存恢复引脚状态
const restorePinState = (pinId) => {
  if (pinId && pinStateCache.value.has(pinId)) {
    const savedState = pinStateCache.value.get(pinId);
    return savedState;
  }
  return null;
};

// 监听props变化，更新表单数据
watch(() => props.pinData, (newData, oldData) => {
  if (newData && newData.pinId) {
    // 只有在引脚ID变化时才进行完整的重新初始化
    const isNewPin = newData.pinId !== lastProcessedPinId.value;

    if (isNewPin) {
      // 保存上一个引脚的状态
      if (lastProcessedPinId.value) {
        savePinState(lastProcessedPinId.value);
      }
      // 设置内部更新标志
      isInternalUpdate.value = true;
      lastProcessedPinId.value = newData.pinId;

      // 尝试从缓存恢复引脚状态
      const savedState = restorePinState(newData.pinId);

      if (savedState) {
        // 如果有保存的状态，恢复它
        Object.assign(formData, savedState);
        originalData.value = { ...savedState };
        isInternalUpdate.value = false;
      } else {
        // 如果没有保存的状态，进行正常初始化
        // 使用明确的字段赋值而不是Object.assign
        formData.pinId = newData.pinId || '';
        formData.pinName = newData.pinName || '';
        formData.pinType = newData.pinType || '';
        formData.desc = newData.desc || '';
        formData.status = newData.status || '可用';
        formData.electricalType = newData.electricalType || 'TTL';

        // 保留动态配置字段
        if (newData.dynamicConfig) {
          formData.dynamicConfig = { ...newData.dynamicConfig };
        }

        originalData.value = { ...newData };

        // 如果有引脚类型，初始化动态配置
        if (newData.pinType) {
          nextTick(() => {
            initializeDynamicConfig(newData.pinType);
            // 确保IO配置字段有正确的默认值
            loadAttributeConfigByPinId(newData.pinType, newData.pinId);

            // 延迟重置标志，确保所有异步操作完成
            setTimeout(() => {
              isInternalUpdate.value = false;
            }, 100);
          });
        } else {
          isInternalUpdate.value = false;
        }
      }
    } else {
      // 如果是同一个引脚，只更新基本信息，不重新初始化动态配置
      if (!isInternalUpdate.value) {
        formData.pinName = newData.pinName || formData.pinName;
        formData.desc = newData.desc || formData.desc;
        formData.status = newData.status || formData.status;
        formData.electricalType = newData.electricalType || formData.electricalType;

        // 保留用户已经修改的动态配置
        if (newData.dynamicConfig && Object.keys(formData.dynamicConfig).length === 0) {
          formData.dynamicConfig = { ...newData.dynamicConfig };
        }
      }
    }
  }
}, { immediate: true, deep: true });

// 获取当前引脚类型的attribute_config显示信息
const currentAttributeConfigInfo = computed(() => {
  if (!formData.pinType) return null;

  const attributeConfigArray = getAttributeConfigForPinType(formData.pinType);
  if (!attributeConfigArray || attributeConfigArray.length === 0) return null;

  // 获取attribute_define来了解字段的display名称
  const typeConfig = currentTypeConfig.value;
  if (!typeConfig) return null;

  // 选择第一个启用的配置项，或者第一个配置项
  const configItem = attributeConfigArray.find(item => item.enable_config) || attributeConfigArray[0];
  if (!configItem) return null;

  const displayInfo = [];

  // 处理基本字段
  if (configItem.name && typeConfig.name) {
    displayInfo.push({
      key: 'name',
      display: typeConfig.name.display,
      value: configItem.name
    });
  }

  if (configItem.des && typeConfig.des) {
    displayInfo.push({
      key: 'des',
      display: typeConfig.des.display,
      value: configItem.des
    });
  }

  if (configItem.pin_id) {
    displayInfo.push({
      key: 'pin_id',
      display: '引脚编号',
      value: configItem.pin_id
    });
  }

  // 处理config子对象中的字段
  if (configItem.config && typeConfig.config) {
    Object.keys(configItem.config).forEach(configKey => {
      if (typeConfig.config[configKey]) {
        displayInfo.push({
          key: configKey,
          display: typeConfig.config[configKey].display,
          value: configItem.config[configKey]
        });
      }
    });
  }

  return {
    configItem,
    displayInfo,
    isEnabled: configItem.enable_config
  };
});

// 监听引脚编号变化，自动匹配attribute_config
watch(() => formData.pinId, (newPinId, oldPinId) => {
  if (newPinId !== oldPinId && newPinId && formData.pinType && !isInternalUpdate.value) {
    loadAttributeConfigByPinId(formData.pinType, newPinId);
  }
});

// 获取指定引脚类型的attribute_config数组
const getAttributeConfigForPinType = (pinType) => {
  const chipFunctions = PinValue.Config_Onboard.Chip_function;

  for (const funcGroup of chipFunctions) {
    if (funcGroup[pinType] && funcGroup[pinType].attribute_config) {
      return funcGroup[pinType].attribute_config;
    }
  }

  return [];
};

// 根据pin_id匹配attribute_config中的配置项
const getAttributeConfigByPinId = (pinType, pinId) => {
  const attributeConfigArray = getAttributeConfigForPinType(pinType);
  if (!attributeConfigArray || attributeConfigArray.length === 0) return null;

  // 首先查找直接匹配pin_id的配置项（单引脚配置）
  const directMatch = attributeConfigArray.find(item =>
    item.pin_id && item.pin_id.toString() === pinId.toString()
  );

  if (directMatch) {
    return directMatch;
  }

  // 查找多引脚配置中包含该pin_id的配置项
  for (const configItem of attributeConfigArray) {
    if (configItem.PinInfo && Array.isArray(configItem.PinInfo)) {
      // 检查PinInfo数组中是否包含当前pin_id
      const matchedPin = configItem.PinInfo.find(pinInfo => {
        // pinInfo格式: [pin_id, type, alt_value]
        if (Array.isArray(pinInfo) && pinInfo.length >= 1) {
          return pinInfo[0].toString() === pinId.toString();
        }
        return false;
      });

      if (matchedPin) {
        // 🔧 将多引脚配置展开为单引脚配置
        const expandedConfig = {
          ...configItem,
          PinInfo: matchedPin[0], // 只保留当前引脚的pin_id
          pin_id: matchedPin[0],  // 添加pin_id字段便于后续处理
          // 如果需要保留引脚的类型和复用值信息
          pin_type: matchedPin[1] || 'GPIO',
          alt_value: matchedPin[2] || '0'
        };

        console.log(`🔧 多引脚配置展开: 原配置名称=${configItem.name}, 引脚=${pinId}, 展开后配置:`, expandedConfig);
        return expandedConfig;
      }
    }
  }

  return null;
};

// 根据pin_id加载对应的attribute_config数据到表单
const loadAttributeConfigByPinId = (pinType, pinId) => {
  console.log(`loadAttributeConfigByPinId 被调用: pinType=${pinType}, pinId=${pinId}`);
  const matchedConfig = getAttributeConfigByPinId(pinType, pinId);
  console.log(`匹配到的配置:`, matchedConfig);

  if (matchedConfig) {
    // 加载基本字段的值
    if (matchedConfig.name && currentBasicFields.value.find(field => field.key === 'name')) {
      formData.dynamicConfig['name'] = matchedConfig.name;
      formData.dynamicConfig['name_hint'] = matchedConfig.name;
    }

    if (matchedConfig.des && currentBasicFields.value.find(field => field.key === 'des')) {
      formData.dynamicConfig['des'] = matchedConfig.des;
      formData.dynamicConfig['des_hint'] = matchedConfig.des;
    }

    // 加载config子对象中的配置值
    if (matchedConfig.config) {
      console.log(`加载config配置:`, matchedConfig.config);
      currentConfigFields.value.forEach(field => {
        if (matchedConfig.config[field.key] !== undefined) {
          console.log(`设置 ${field.key} = ${matchedConfig.config[field.key]}`);
          formData.dynamicConfig[field.key] = matchedConfig.config[field.key];
        }
      });
    }

    // 加载PinInfo字段的值，特别是当只有一个选项时设置默认值
    currentPinInfoFields.value.forEach(pinInfo => {
      Object.keys(pinInfo.config).forEach(fieldKey => {
        const fieldConfig = pinInfo.config[fieldKey];
        const configKey = `${pinInfo.key}_${fieldKey}`;

        // 如果是枚举类型且只有一个选项，自动设置为默认值
        if (fieldConfig.type === 'enum' && fieldConfig.list && fieldConfig.list.length === 1) {
          formData.dynamicConfig[configKey] = fieldConfig.list[0];
        }
        // 如果是枚举类型且有多个选项但没有设置值，也设置第一个为默认值
        else if (fieldConfig.type === 'enum' && fieldConfig.list && fieldConfig.list.length > 0 && !formData.dynamicConfig[configKey]) {
          formData.dynamicConfig[configKey] = fieldConfig.list[0];
        }
      });
    });

    // 在内部更新时不触发表单变化事件，避免递归
    if (!isInternalUpdate.value) {
      handleFormChange();
    }
  }
};

// 获取当前匹配的配置信息
const getMatchedConfigInfo = () => {
  if (!formData.pinId || !formData.pinType) return null;
  return getAttributeConfigByPinId(formData.pinType, formData.pinId);
};

// 获取匹配状态
const getMatchedConfigStatus = () => {
  return getMatchedConfigInfo() !== null;
};

// 获取attribute_config中的提示值（用于显示在字段后面）
const getAttributeConfigHint = (fieldKey) => {
  if (!formData.pinType) return null;

  let configItem = null;

  // 如果有选择的引脚编号，优先根据pin_id匹配
  if (formData.pinId) {
    configItem = getAttributeConfigByPinId(formData.pinType, formData.pinId);
  }

  // 如果没有匹配到或没有选择引脚编号，使用默认逻辑
  if (!configItem) {
    const attributeConfigArray = getAttributeConfigForPinType(formData.pinType);
    if (!attributeConfigArray || attributeConfigArray.length === 0) return null;

    // 选择第一个启用的配置项，或者第一个配置项
    configItem = attributeConfigArray.find(item => item.enable_config) || attributeConfigArray[0];
  }

  if (!configItem) return null;

  // 如果字段在config子对象中
  if (configItem.config && configItem.config[fieldKey] !== undefined) {
    return configItem.config[fieldKey];
  }

  // 如果字段在根级别
  if (configItem[fieldKey] !== undefined) {
    return configItem[fieldKey];
  }

  return null;
};

// 获取attribute_config中的默认值
const getAttributeConfigDefault = (fieldKey, pinType) => {
  console.log(`获取默认值: fieldKey=${fieldKey}, pinType=${pinType}`);

  // 🔧 优先根据当前引脚ID获取匹配的配置
  if (formData.pinId) {
    const matchedConfig = getAttributeConfigByPinId(pinType, formData.pinId);
    if (matchedConfig) {
      console.log(`根据引脚ID ${formData.pinId} 找到匹配配置:`, matchedConfig);

      // 如果字段在config子对象中
      if (matchedConfig.config && matchedConfig.config[fieldKey] !== undefined) {
        console.log(`在匹配配置的config中找到 ${fieldKey}:`, matchedConfig.config[fieldKey]);
        return matchedConfig.config[fieldKey];
      }

      // 如果字段在根级别
      if (matchedConfig[fieldKey] !== undefined) {
        console.log(`在匹配配置的根级别找到 ${fieldKey}:`, matchedConfig[fieldKey]);
        return matchedConfig[fieldKey];
      }
    }
  }

  // 🔧 如果没有匹配的引脚配置，使用原有逻辑作为后备
  // 找到对应引脚类型的Chip_function配置
  const chipFunctions = PinValue.Config_Onboard.Chip_function;
  let attributeConfigArray = null;

  for (const funcGroup of chipFunctions) {
    if (funcGroup[pinType] && funcGroup[pinType].attribute_config) {
      attributeConfigArray = funcGroup[pinType].attribute_config;
      break;
    }
  }

  console.log(`找到的attribute_config数组:`, attributeConfigArray);

  if (!attributeConfigArray || !Array.isArray(attributeConfigArray)) return null;

  // 从attribute_config数组中查找第一个启用的配置项，或者第一个配置项
  const configItem = attributeConfigArray.find(item => item.enable_config) || attributeConfigArray[0];

  console.log(`选中的配置项:`, configItem);

  if (!configItem) return null;

  // 如果字段在config子对象中
  if (configItem.config && configItem.config[fieldKey] !== undefined) {
    console.log(`在config中找到 ${fieldKey}:`, configItem.config[fieldKey]);
    return configItem.config[fieldKey];
  }

  // 如果字段在根级别
  if (configItem[fieldKey] !== undefined) {
    console.log(`在根级别找到 ${fieldKey}:`, configItem[fieldKey]);
    return configItem[fieldKey];
  }

  // 调试：检查是否从其他地方获取了错误的值
  console.log(`检查currentTypeConfig.value:`, currentTypeConfig.value);
  if (currentTypeConfig.value && currentTypeConfig.value.config && currentTypeConfig.value.config[fieldKey]) {
    console.log(`从currentTypeConfig.value.config获取到 ${fieldKey}:`, currentTypeConfig.value.config[fieldKey]);
    // 这里可能是问题所在 - 不应该返回字段定义，而应该返回实际值
    if (typeof currentTypeConfig.value.config[fieldKey] === 'object' && currentTypeConfig.value.config[fieldKey].type) {
      console.log(`警告：返回的是字段定义而不是值！`);
      return null; // 不返回字段定义
    }
  }

  console.log(`未找到字段 ${fieldKey} 的默认值`);
  return null;
};

// 初始化动态配置字段
const initializeDynamicConfig = (pinType) => {
  if (!currentTypeConfig.value) {
    return;
  }

  // 清空之前的动态配置
  formData.dynamicConfig = {};

  // 从attribute_config中获取基础信息并填入表单
  // 获取当前引脚类型的attribute_config数组
  const chipFunctions = PinValue.Config_Onboard.Chip_function;
  let attributeConfigArray = null;

  for (const funcGroup of chipFunctions) {
    if (funcGroup[pinType] && funcGroup[pinType].attribute_config) {
      attributeConfigArray = funcGroup[pinType].attribute_config;
      break;
    }
  }

  if (attributeConfigArray && Array.isArray(attributeConfigArray)) {
    // 选择第一个启用的配置项，或者第一个配置项作为默认值
    const defaultConfigItem = attributeConfigArray.find(item => item.enable_config) || attributeConfigArray[0];

    if (defaultConfigItem) {
      // name填入通道名称
      if (defaultConfigItem.name) {
        const nameField = currentBasicFields.value.find(field => field.key === 'name');
        if (nameField) {
          formData.dynamicConfig['name'] = defaultConfigItem.name;
          // 初始化hint字段
          formData.dynamicConfig['name_hint'] = defaultConfigItem.name;
        }
      }

      // des填入引脚注释
      if (defaultConfigItem.des) {
        const desField = currentBasicFields.value.find(field => field.key === 'des');
        if (desField) {
          formData.dynamicConfig['des'] = defaultConfigItem.des;
          // 初始化hint字段
          formData.dynamicConfig['des_hint'] = defaultConfigItem.des;
        }
      }

      // pin_id对应引脚编号 - 可选设置
      if (defaultConfigItem.pin_id) {
        // 如果需要自动设置引脚编号，可以取消下面的注释
        // formData.pinId = defaultConfigItem.pin_id.toString();
      }
    }
  }

  // 初始化基本字段的默认值
  currentBasicFields.value.forEach(field => {
    let defaultValue = null;

    // 优先使用attribute_config中的默认值
    const attributeDefault = getAttributeConfigDefault(field.key, pinType);
    if (attributeDefault !== null) {
      defaultValue = attributeDefault;
    } else if (field.default !== undefined) {
      defaultValue = field.default;
    } else if (field.type === 'enum' && field.list && field.list.length > 0) {
      // 如果是枚举类型且没有默认值，使用第一个选项作为默认值
      defaultValue = field.list[0];
    } else if (field.type === 'string') {
      // 字符串类型默认为空字符串
      defaultValue = '';
    }

    if (defaultValue !== null) {
      formData.dynamicConfig[field.key] = defaultValue;
    }
  });

  // 初始化PinInfo字段的默认值
  currentPinInfoFields.value.forEach(pinInfo => {
    Object.keys(pinInfo.config).forEach(fieldKey => {
      const fieldConfig = pinInfo.config[fieldKey];
      const configKey = `${pinInfo.key}_${fieldKey}`;
      let defaultValue = null;

      // 对于pin_id字段，应该使用list中的值而不是attribute_config中的值
      if (fieldKey === 'pin_id' && fieldConfig.type === 'enum' && fieldConfig.list && fieldConfig.list.length > 0) {
        // 如果list只有一个值，直接使用它
        defaultValue = fieldConfig.list[0];
      } else {
        // 其他字段优先使用attribute_config中的默认值
        const attributeDefault = getAttributeConfigDefault(fieldKey, pinType);
        if (attributeDefault !== null) {
          defaultValue = attributeDefault;
        } else if (fieldConfig.default !== undefined) {
          defaultValue = fieldConfig.default;
        } else if (fieldConfig.type === 'enum' && fieldConfig.list && fieldConfig.list.length > 0) {
          // 如果是枚举类型且没有默认值，使用第一个选项作为默认值
          defaultValue = fieldConfig.list[0];
        } else if (fieldConfig.type === 'string') {
          // 字符串类型默认为空字符串
          defaultValue = '';
        }
      }

      if (defaultValue !== null) {
        formData.dynamicConfig[configKey] = defaultValue;
      }
    });
  });

  // 初始化配置参数字段的默认值
  currentConfigFields.value.forEach(field => {
    console.log(`初始化字段 ${field.key}:`, field);

    // 🔧 修复：如果字段已经有值，不要覆盖它
    if (formData.dynamicConfig[field.key] !== undefined) {
      return;
    }

    let defaultValue = null;

    // 优先使用attribute_config中的默认值
    const attributeDefault = getAttributeConfigDefault(field.key, pinType);
    console.log(`${field.key} 的 attributeDefault:`, attributeDefault);

    if (attributeDefault !== null) {
      defaultValue = attributeDefault;
    } else if (field.default !== undefined) {
      defaultValue = field.default;
    } else if (field.type === 'enum' && field.list && field.list.length > 0) {
      // 如果是枚举类型且没有默认值，使用第一个选项作为默认值
      defaultValue = field.list[0];
    } else if (field.type === 'string' || field.type === 'label') {
      // 字符串类型和label类型默认为空字符串
      defaultValue = '';
    } else if (field.type && field.type.includes('uint')) {
      // 数值类型使用最小值或0作为默认值
      defaultValue = field.min || 0;
    } else {
      // 其他类型默认为空字符串
      defaultValue = '';
    }

    console.log(`${field.key} 的最终 defaultValue:`, defaultValue);

    if (defaultValue !== null) {
      formData.dynamicConfig[field.key] = defaultValue;
      console.log(`设置 formData.dynamicConfig[${field.key}] =`, defaultValue);
    }
  });


};

// 表单变化处理
const handleFormChange = () => {
  // 只有在非内部更新时才发出事件，防止递归调用
  if (!isInternalUpdate.value) {
    // 自动保存当前引脚状态
    if (formData.pinId) {
      savePinState(formData.pinId);
    }

    // 发出change事件，但不发出update:pinData事件，避免循环更新
    emit('change', { ...formData });
  }
};

// 保存数据到父组件
const handleSave = () => {
  emit('save', { ...formData });
  emit('update:pinData', { ...formData });
};

// 引脚类型变化处理
const handlePinTypeChange = () => {
  // 设置内部更新标志，防止递归
  isInternalUpdate.value = true;

  // 清空动态配置
  formData.dynamicConfig = {};

  // 重新初始化动态配置
  if (formData.pinType) {
    nextTick(() => {
      initializeDynamicConfig(formData.pinType);
      // 如果有引脚ID，加载对应的attribute_config
      if (formData.pinId) {
        loadAttributeConfigByPinId(formData.pinType, formData.pinId);
      }

      // 延迟重置标志，确保所有更新完成
      setTimeout(() => {
        isInternalUpdate.value = false;
        handleFormChange();
      }, 50);
    });
  } else {
    isInternalUpdate.value = false;
    handleFormChange();
  }
};

// 功能模块配置相关计算属性
const showFunctionModuleConfig = computed(() => {
  // 只有当选择了引脚类型且不是GPIO类型时才显示功能模块配置
  return formData.pinType && formData.pinType !== 'GPIO';
});

const canLoadRepositoryInfo = computed(() => {
  return props.repositoryInfo &&
         (props.repositoryInfo.gitlab || props.repositoryInfo.project_branch);
});

// 功能模块配置相关方法
const loadRepositoryInfo = () => {
  if (!props.repositoryInfo) {
    console.warn('没有可用的仓库信息');
    return;
  }

  // 从芯片配置加载仓库和分支信息到功能模块配置
  functionModuleConfig.gitlab = props.repositoryInfo.gitlab || '';
  functionModuleConfig.project_branch = props.repositoryInfo.project_branch || '';
  functionModuleConfig.workspace = props.repositoryInfo.workspace || '';
  functionModuleConfig.branch_status = props.repositoryInfo.branch_status || '';

  console.log('已加载仓库信息到功能模块配置:', functionModuleConfig);

  // 触发表单变化事件
  handleFormChange();
};

const clearFunctionModuleConfig = () => {
  functionModuleConfig.gitlab = '';
  functionModuleConfig.project_branch = '';
  functionModuleConfig.workspace = '';
  functionModuleConfig.branch_status = '';

  console.log('已清空功能模块配置');

  // 触发表单变化事件
  handleFormChange();
};

// 监听引脚类型变化，自动加载仓库信息
watch(() => formData.pinType, (newType, oldType) => {
  if (newType && newType !== 'GPIO' && newType !== oldType) {
    // 当切换到非GPIO类型时，自动加载仓库信息
    if (canLoadRepositoryInfo.value) {
      loadRepositoryInfo();
    }
  }
});


</script>

<style scoped>
.dynamic-pin-editor {
  height: 100%;
}

.card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.card-title {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-header {
  margin: 20px 0 15px 0;
}

.section-title {
  color: #409eff;
  font-weight: 600;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.dynamic-config-area {
  margin-top: 20px;
}

.config-section {
  margin-bottom: 25px;
  padding: 15px;
  background: #fafbfc;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.pin-info-group {
  margin-bottom: 20px;
  /* padding: 15px; */
  /* background: white; */
  /* border-radius: 6px; */
  /* border: 1px solid #e1f5fe; */
}

.pin-info-title {
  margin: 0 0 15px 0;
  color: #0ea5e9;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 6px;
}

.pin-info-title::before {
  content: "📌";
  font-size: 16px;
}

.form-left-align {
  margin-top: 15px;
}

.form-left-align .el-form-item {
  margin-bottom: 18px;
}

/* 统一label样式和左对齐 */
.form-left-align .el-form-item__label {
  font-weight: 500;
  color: #606266;
  text-align: left !important;
  justify-content: flex-start !important;
  width: 140px !important;
  min-width: 140px !important;
  max-width: 140px !important;
  padding-right: 12px !important;
  padding-left: 0 !important;
  display: flex !important;
  align-items: center !important;
  flex-shrink: 0 !important;
}

/* 更强的选择器确保label左对齐 */
.dynamic-pin-editor .form-left-align .el-form-item .el-form-item__label {
  text-align: left !important;
  justify-content: flex-start !important;
  width: 140px !important;
  min-width: 140px !important;
  max-width: 140px !important;
}

/* 针对Element Plus的label-left模式 */
.dynamic-pin-editor .el-form--label-left .el-form-item__label {
  text-align: left !important;
  justify-content: flex-start !important;
  width: 140px !important;
}

.button-group {
  display: flex;
  gap: 10px;
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #e4e7ed;
}

.button-group .el-button {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 滚动条样式 */
.dynamic-config-area::-webkit-scrollbar {
  width: 6px;
}

.dynamic-config-area::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.dynamic-config-area::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.dynamic-config-area::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 字段提示样式 */
.field-with-hint {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.config-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  padding-left: 2px;
  font-style: italic;
}

/* 可编辑的配置提示输入框样式 */
.config-hint-input {
  margin-top: 8px;
}

.config-hint-input .el-input__inner {
  font-size: 12px;
  height: 28px;
  line-height: 28px;
  border: 1px dashed #d9d9d9;
  background-color: #fafafa;
  color: #666;
}

.config-hint-input .el-input__inner:focus {
  border-color: #409eff;
  background-color: #fff;
}

.config-hint-input .el-input__inner::placeholder {
  color: #909399;
  font-style: italic;
}

/* 匹配配置信息样式 */
.matched-config-section {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
}

.config-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.config-info-item {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.config-info-label {
  font-weight: 500;
  color: #606266;
  font-size: 14px;
}

.config-info-value {
  color: #303133;
  font-size: 14px;
  font-weight: 500;
}

.no-match-info {
  margin-top: 15px;
}

/* 内容延展动画 */
.config-section {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 功能模块配置样式 */
.function-module-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}

.function-module-actions .el-button {
  flex: 1;
}

/* 表单项动画 */
.form-left-align .el-form-item {
  animation: fadeIn 0.2s ease-out;
  animation-fill-mode: both;
}

.form-left-align .el-form-item:nth-child(1) { animation-delay: 0.1s; }
.form-left-align .el-form-item:nth-child(2) { animation-delay: 0.2s; }
.form-left-align .el-form-item:nth-child(3) { animation-delay: 0.3s; }
.form-left-align .el-form-item:nth-child(4) { animation-delay: 0.4s; }
.form-left-align .el-form-item:nth-child(5) { animation-delay: 0.5s; }

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dynamic-pin-editor {
    min-height: 400px;
  }

  .card {
    padding: 15px;
  }

  .button-group {
    flex-direction: column;
    position: relative; /* 移动端不使用sticky */
  }

  .button-group .el-button {
    width: 100%;
    justify-content: center;
  }

  .config-section {
    padding: 10px;
    margin-bottom: 15px;
  }

  .dynamic-config-area {
    padding-right: 0;
  }

  .config-info-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .field-with-hint {
    flex-direction: column;
    align-items: stretch;
  }

  .config-hint {
    font-size: 10px;
    margin-top: 2px;
  }
}

/* 深色模式支持 - 保持与普通模式一致 */
@media (prefers-color-scheme: dark) {
  .card {
    background: white;
    color: #303133;
  }

  .config-section {
    background: #fafbfc;
    border-color: #e4e7ed;
  }

  .pin-info-group {
    background: white;
    border-color: #e1f5fe;
  }

  .attribute-config-section {
    background: #fafbfc;
    border-color: #e4e7ed;
  }

  .config-info-item {
    background: white;
    border-color: #e4e7ed;
  }
}
</style>
