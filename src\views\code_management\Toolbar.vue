<template>
  <!-- 顶部工具栏 -->
  <div class="toolbar">
    <div class="toolbar-left">
      <!-- 左侧版本标识 -->
      <div class="version-info">
        <el-tag type="info" effect="plain" :style="{ border: 'none'}">
          <el-icon style="color: #409eff;"><InfoFilled /></el-icon>
          版本: {{ sdkVersion || 'N/A' }}
        </el-tag>
      </div>
    </div>
    <div class="toolbar-right">
      <!-- 工作空间和分支 -->
      <div class="config-selects">
        <el-form-item label="项目仓库:" class="toolbar-form-item" style="font-size: 14px; font-weight: 300; color: #666;">
          <el-select
            :model-value="form.gitlab"
            @update:model-value="updateGitlab"
            class="input-field"
            placeholder="请选择或输入仓库地址"
            filterable
            allow-create
            clearable
            style="border: none; padding: 0px"
            @visible-change="handleGitlabClick"
          >
            <el-option
              v-for="item in spaceOptions"
              :key="item"
              :label="item.split('/').pop()"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="工作空间:" class="toolbar-form-item" style="font-size: 14px; font-weight: 300; color: #666;">
          <el-select
            :model-value="form.project_branch"
            @update:model-value="updateBranch"
            class="input-field"
            placeholder="请选择或输入分支"
            filterable
            allow-create
            clearable
            style="border: none; padding: 0px;"
          >
            <el-option
              v-for="branch in branchOptions"
              :key="branch"
              :label="branch"
              :value="branch"
            />
          </el-select>
        </el-form-item>
      </div>
      <!-- 功能按钮 -->
      <div class="button-group">
        <el-button type="primary" @click="handleSave">
          Commit
        </el-button>
        <el-button type="success" @click="handlePublish">
          Push
        </el-button>
        <el-button type="danger" @click="mergetest">
          Merge
        </el-button>
      </div>
    </div>
    
    <!-- 合并分支弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      title="选择合并分支"
      width="400px"
      :show-close="true"
      center
      class="merge-dialog"
    >
      <div class="dialog-content">
        <el-form label-position="top">
          <el-form-item label="目标分支">
            <el-select v-model="mergeBranch" :placeholder="mergeBranch" style="width: 100%">
              <el-option v-for="item in ['dev']" :key="item" :label="item" :value="item">
                {{ item }}
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmAction">确定合并</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { InfoFilled } from '@element-plus/icons-vue';

// Props
const props = defineProps({
  sdkVersion: {
    type: String,
    default: ''
  },
  form: {
    type: Object,
    required: true
  },
  spaceOptions: {
    type: Array,
    default: () => []
  },
  branchOptions: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits([
  'update:form',
  'gitlab-click',
  'save',
  'publish',
  'merge-test',
  'confirm-action'
]);

// 响应式数据
const dialogVisible = ref(false);
const mergeBranch = ref('dev');

// 直接使用props中的form，通过事件更新
const updateGitlab = (value) => {
  emit('update:form', { ...props.form, gitlab: value });
};

const updateBranch = (value) => {
  emit('update:form', { ...props.form, project_branch: value });
};

// 事件处理函数
const handleGitlabClick = (visible) => {
  emit('gitlab-click', visible);
};

const handleSave = () => {
  emit('save');
};

const handlePublish = () => {
  emit('publish');
};

const mergetest = () => {
  dialogVisible.value = true;
  emit('merge-test');
};

const confirmAction = () => {
  dialogVisible.value = false;
  emit('confirm-action', mergeBranch.value);
};
</script>

<style scoped>
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.toolbar-left {
  display: flex;
  align-items: center;
}

.version-info {
  margin-right: 20px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.config-selects {
  display: flex;
  align-items: center;
  gap: 16px;
}

.toolbar-form-item {
  margin-bottom: 0;
}

.input-field {
  min-width: 150px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.merge-dialog {
  border-radius: 8px;
}

.dialog-content {
  padding: 20px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>
