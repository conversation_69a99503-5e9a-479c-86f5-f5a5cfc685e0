# 项目状态共享功能说明

## 功能概述

实现了代码管理模块中项目仓库和工作空间选择状态的全局共享，确保在芯片配置页面选择了项目仓库和工作空间后，跳转到功能模块（如GPIO配置）时能够自动使用已选择的配置。

## 核心特性

### 1. 全局状态管理
- 扩展了 `useProjectStore` 添加 `codeManagement` 状态
- 包含仓库地址、分支、仓库选项列表、分支选项列表、SDK版本等信息
- 使用 sessionStorage 持久化存储，页面刷新后状态不丢失

### 2. 状态同步机制
- 在config页面和GPIO页面选择仓库/分支时，自动同步到全局状态
- 页面初始化时从全局状态恢复之前的选择
- 项目切换时自动清空代码管理相关状态

### 3. 智能初始化
- 页面加载时检查全局状态中是否有已选择的仓库和分支
- 如果有完整信息，直接使用并提交配置
- 如果信息不完整，按需获取缺失的数据（仓库列表→分支列表→提交配置）

## 技术实现

### Store 扩展 (`src/stores/project.js`)

```javascript
state: () => ({
  project_info: {},
  codeManagement: {
    gitlab: '',           // 选择的项目仓库
    project_branch: '',   // 选择的分支
    spaceOptions: [],     // 可用的仓库列表
    branchOptions: [],    // 可用的分支列表
    sdkVersion: '',       // SDK版本
  }
})
```

### 新增方法
- `updateCodeManagement(data)` - 更新代码管理状态
- `clearCodeManagement()` - 清空代码管理状态
- `setRepositoryInfo(gitlab, branch)` - 设置仓库信息
- `setSpaceOptions(options)` - 设置仓库选项
- `setBranchOptions(options)` - 设置分支选项
- `setSdkVersion(version)` - 设置SDK版本

### 页面改动

#### Config页面 (`src/views/code_management/config/index.vue`)
- 添加 `initializeFromStore()` 函数从全局状态恢复数据
- 修改初始化逻辑，支持智能恢复和按需获取
- 在仓库/分支变化时同步更新全局状态
- 项目切换时清空全局状态

#### GPIO页面 (`src/views/code_management/gpio/index.vue`)
- 实现与config页面相同的状态管理逻辑
- 确保两个页面的状态完全同步

## 使用流程

### 典型使用场景
1. 用户在芯片配置页面选择项目仓库和分支
2. 状态自动保存到全局store中
3. 用户跳转到GPIO配置页面
4. GPIO页面自动从全局状态恢复之前的选择
5. 无需重新选择，直接使用已配置的仓库和分支

### 状态恢复逻辑
```javascript
// 页面初始化时的智能恢复
if (!form.gitlab || spaceOptions.value.length === 0) {
  // 没有仓库信息，获取仓库列表
  get_space();
} else if (!form.project_branch || branchOptions.value.length === 0) {
  // 有仓库但没有分支信息，获取分支列表
  get_branch();
} else {
  // 都有，直接提交配置
  submit_branch_info();
}
```

## 测试页面

创建了测试页面 `src/views/code_management/test-shared-state.vue` 用于验证功能：
- 实时显示全局状态信息
- 监听状态变化并记录日志
- 提供清空状态和页面跳转功能
- 访问路径: `/code_management/test-shared-state`

## 注意事项

1. **状态持久化**: 使用 sessionStorage，关闭浏览器后状态会丢失
2. **项目切换**: 切换项目时会自动清空代码管理状态，避免状态污染
3. **向后兼容**: 保持原有API不变，新功能为增强型功能
4. **错误处理**: 保留原有的错误处理逻辑，增加状态同步的容错机制

## 未来扩展

- 可以考虑将状态持久化改为 localStorage 以支持跨会话保存
- 可以扩展到其他代码管理相关的页面
- 可以添加状态变化的事件通知机制
