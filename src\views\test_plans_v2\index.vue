<template>

    <el-container>

        <el-header>
            <el-tabs v-model="activeTabName" class="tabs" @tab-click="handleTabClick">
                <el-tab-pane label="所有测试活动类型" name=""></el-tab-pane>
                <el-tab-pane label="系统合格性测试" name="SAT"></el-tab-pane>
                <el-tab-pane label="系统集成测试" name="IST"></el-tab-pane>
                <el-tab-pane label="软件合格性测试" name="SQT"></el-tab-pane>
                <el-tab-pane label="软件集成测试" name="SIT"></el-tab-pane>
                <el-tab-pane label="软件单元测试" name="SUT"></el-tab-pane>
                <el-tab-pane label="硬件测试" name="HT"></el-tab-pane>
                <el-tab-pane label="我创建的" name="MY_CREATED"></el-tab-pane>
            </el-tabs>
        </el-header>


        <el-main>

            <div style="display: flex; flex-direction: column; height: calc(104vh - 350px);">
                <div class="tool-bar-container">
                    <el-button icon="Plus" type="primary" @click="handleAdd">新增</el-button>

                    <div style="margin-left: auto; display: flex; gap: 10px;">
                        <div>
                            <el-button icon="Setting" text bg @click="isSettingVisible = true">设置</el-button>
                            <!-- 设置弹窗 -->
                            <el-popover v-model:visible="isSettingVisible" width="180" trigger="manual"
                                placement="bottom">
                                <template #reference>
                                    <!-- 设置按钮作为触发器 -->
                                    <div style="display: inline-block;"></div>
                                </template>
                                <!-- 操作按钮 -->
                                <div class="column-popper-title">
                                    <div style="display: flex; align-items: center; justify-content: space-between;">
                                        <el-checkbox :model-value="tableColumns.every(item => item.show)"
                                            :indeterminate="tableColumns.some(item => item.show) && tableColumns.some(item => !item.show)"
                                            label="列展示" @change="selectAllColumn" />
                                        <el-button text @click="resetColumns"
                                            style="margin-right: -10px;">重置</el-button>
                                    </div>
                                </div>
                                <!-- 列设置内容 -->
                                <div class="column-content" style="max-height: 200px; overflow-y: auto;">
                                    <div class="column-item" v-for="column in tableColumns" :key="column.key">
                                        <el-checkbox v-model="column.show" :label="column.name"
                                            :disabled="column.disabled"></el-checkbox>
                                    </div>
                                </div>
                            </el-popover>

                        </div>
                        <el-tooltip class="box-item" effect="dark" content="重置已添加的筛选条件" placement="top-start">
                            <el-button text bg @click="handleReset">重置</el-button>
                        </el-tooltip>
                        <filterButton @click="onFilterStatusChange" :count="filterCount" />
                        <el-button icon="Refresh" text bg @click="handleRefresh">刷新</el-button>
                    </div>
                </div>

                <div class="filter-container" v-if="showFilterContainer">
                    <el-input v-model="form.name_re" placeholder="请输入计划名称" @keyup.enter="onFilter" clearable>
                        <template #append>
                            <el-button icon="Search" @click="onFilter"></el-button>
                        </template>
                    </el-input>
                    <el-input v-model="form.creator_name_re" placeholder="请输入创建人" @keyup.enter="onFilter" clearable>
                        <template #append>
                            <el-button icon="Search" @click="onFilter"></el-button>
                        </template>
                    </el-input>
                    <el-select v-model="form.plan_type_list" placeholder="请选择计划类型" @change="onFilter"
                        style="width: 400px;" multiple clearable>
                        <el-option label="内部验证" :value="1"></el-option>
                        <el-option label="对外发布" :value="0"></el-option>
                    </el-select>
                    <el-input v-model="form.m_version" placeholder="请输入产品软件版本" @keyup.enter="onFilter" clearable>
                        <template #append>
                            <el-button icon="Search" @click="onFilter"></el-button>
                        </template>
                    </el-input>
                </div>

        <el-table :data="tableData" stripe border style="width: 100%;flex: 1;" class="table-container">
            <el-table-column v-if="tableColumns[0].show" prop="project_name" label="项目" min-width="300"
                align="center"></el-table-column>
            <el-table-column v-if="tableColumns[1].show" prop="name" label="计划名称" min-width="200" align="left">
                <template #default="{ row }">
                    <el-link type="primary" @click="onDetail(row)" :underline="false">{{ row.name }}</el-link>
                </template>
            </el-table-column>
            <el-table-column v-if="tableColumns[2].show" label="计划用途" min-width="150" align="center">
                <template #default="{ row }">
                    <el-tag v-if="row.plan_use == 'FULL_FUNCTIONALITY_TEST'" type="success">全功能测试</el-tag>
                    <el-tag v-else-if="row.plan_use == 'VERSION_REGRESSION_TEST'" type="success">版本回归测试</el-tag>
                    <el-tag v-else-if="row.plan_use == 'SPECIFIC_VALIDATION_TEST'" type="success">专项验证测试</el-tag>
                    <el-tag v-else-if="row.plan_use == 'PROBLEM_VALIDATION_TEST'" type="success">问题验证测试</el-tag>
                    <el-tag v-else-if="row.plan_use == 'DURABILITY_TEST'" type="primary">耐久测试</el-tag>
                    <el-tag v-else type="danger">{{ row.plan_use }}</el-tag>
                </template>
            </el-table-column>
            <el-table-column v-if="tableColumns[3].show" label="计划类型" min-width="100" align="center">
                <template #default="{ row }">
                    <el-tag v-if="row.plan_type == 0" type="success">对外发布</el-tag>
                    <el-tag v-else-if="row.plan_type == 1" type="primary">内部验证</el-tag>
                </template>
            </el-table-column>
            <el-table-column v-if="tableColumns[4].show" prop="test_type_name" label="测试类型" min-width="200"
                align="center"></el-table-column>
            <el-table-column v-if="tableColumns[5].show" prop="m_version" label="测试版本" min-width="300"
                align="center"></el-table-column>
            <el-table-column v-if="tableColumns[6].show" prop="status" label="计划状态" min-width="120" align="center">
                <template #default="{ row }">
                    <el-tag v-if="row.status == 'DEBUGGING'" type="info">调试中</el-tag>
                    <el-tag v-else-if="row.status == 'REVIEWING'" type="warning">评审中</el-tag>
                    <el-tag v-else-if="row.status == 'APPROVED'" type="success">评审通过</el-tag>
                    <el-tag v-else-if="row.status == 'REJECTED'" type="danger">评审不通过</el-tag>
                    <el-tag v-else-if="row.status == 'RUNNING'" type="success">执行中</el-tag>
                    <el-tag v-else-if="row.status == 'COMPLETED'" type="primary">已完成</el-tag>
                    <el-tag v-else type="danger">{{ row.status }}</el-tag>
                </template>
            </el-table-column>
            <!-- <el-table-column v-if="tableColumns[7].show" prop="progress" label="执行进度" min-width="150"
                align="center"></el-table-column> -->
            <el-table-column v-if="tableColumns[8].show" prop="creator_name" label="创建人" min-width="100"
                align="center"></el-table-column>
            <el-table-column v-if="tableColumns[9].show" label="测试记录" min-width="100" align="center">
                <template #default="{ row }">
                    <el-link :underline="false" type="primary" @click="handleTestRecord(row)">查看</el-link>
                </template>
            </el-table-column>
            <el-table-column v-if="tableColumns[9].show" prop="p_start_time" label="开始时间" min-width="100" align="center"></el-table-column>
            <el-table-column v-if="tableColumns[10].show" prop="p_end_time" label="结束时间" min-width="100" align="center"></el-table-column>
            <el-table-column v-if="tableColumns[11].show" label="操作" min-width="250" fixed="right" align="center">
                <template #default="{ row }">
                    <div style="display: flex; justify-content: center; gap: 10px;">
                        <el-button @click="handleDelete(row)" type="danger" size="small">删除</el-button>
                        <el-button type="primary" size="small" @click="handleUpdateStatus(row)">变更状态</el-button>
                        <el-button type="primary" size="small" @click="handleCopy(row)">复制</el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>

            </div>

            <div class="pagination-container">
                <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100]"
                    layout="prev, pager, next, jumper, total, sizes" v-model:current-page="form.page"
                    v-model:page-size="form.pagesize" :total="total" background @change="onPageChange" />
            </div>

        </el-main>


    </el-container>



    <el-dialog v-if="dialogVisible" v-model="dialogVisible" title="状态变更" width="600px">
        <StatusUpdate :versionInfo="versionInfo" @cancel="dialogVisible = false" @confirm="onUpdateStatusConfirm" />
    </el-dialog>

    <el-drawer v-model="drawerDetailVisible" :with-header="false" size="70%" :destroy-on-close="true">
        <TestPlanDetail :id="r_id" />
    </el-drawer>

    <el-dialog v-if="dialogCopyVisible" v-model="dialogCopyVisible" title="测试计划复制" width="1200px">
        <Copy :id="r_id" @cancel="dialogCopyVisible = false" @confirm="onCopyConfirm" />
    </el-dialog>

</template>


<script setup>
import { ref, reactive, onMounted, onActivated, watch } from 'vue';
import http from '@/utils/http/http.js';
import { useRouter, useRoute } from 'vue-router';
import { useProjectStore } from '@/stores/project.js';
import filterButton from '@/components/filterButton.vue';
import { useTestPlanStore } from '@/stores/testPlan.js';
import { useTestRecordStore } from '@/stores/testRecord.js';
import StatusUpdate from './status_update.vue';
import TestPlanDetail from './detail.vue';
import Copy from './copy.vue';
import { useAccessStat } from '@/utils/accessStat';

useAccessStat('/test_plans_v2/list', '测试计划列表');

const tableColumns = ref([
    { key: "project_name", name: "项目", show: true, disabled: true },
    { key: "name", name: "计划名称", show: true, disabled: true },
    { key: "plan_use", name: "计划用途", show: true },
    { key: "plan_type", name: "计划类型", show: true },
    { key: "test_type_name", name: "测试类型", show: true },
    { key: "m_version", name: "测试版本", show: true },
    { key: "status", name: "计划状态", show: true },
    { key: "creator_name", name: "创建人", show: true },
    { key: "test_record", name: "测试记录", show: true },
    { key: "p_start_time", name: "开始时间", show: true },
    { key: "p_end_time", name: "结束时间", show: true },
    { key: "operation", name: "操作", show: true }
]);


// 设置弹窗的显示状态
const isSettingVisible = ref(false);

// 全选或取消全选逻辑
const selectAllColumn = (checked) => {
    tableColumns.value.forEach((column) => {
        if (!column.disabled) { // 跳过禁用的列
            column.show = checked;
        }
    });
};


// 重置列设置
const resetColumns = () => {
    tableColumns.value.forEach((column) => {
        if (!column.disabled) { // 跳过禁用的列
            column.show = true;
        }
    });
};

let dialogVisible = ref(false);
const dialogCopyVisible = ref(false);
let versionInfo = ref(null);
const router = useRouter();
const route = useRoute();
let projectStore = useProjectStore();
const filterCount = ref(0);
const tableData = ref([]);
let testPlanStore = useTestPlanStore();
let testRecordStore = useTestRecordStore();
const drawerDetailVisible = ref(false);
let r_id = ref(0);
const activeTabName = ref('');

let form = reactive({
    page: 1,
    pagesize: 15,
    project_number: '',
});

let total = ref(0);

let showFilterContainer = ref(false);

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
};

function handleTabClick(tab) {
    form.action_type = tab.paneName;
    update_table(form);
};

function update_table() {
    http.get('/v2/test_plans', { params: form }).then(res => {
        tableData.value = res.data.data.results;
        total.value = res.data.data.count;
    });

    filterCount.value = Object.keys(form).filter(key => !['page', 'pagesize', 'project_number', 'id'].includes(key)).reduce((count, key) => {
        if (form[key] == '' || form[key] == undefined || form[key] == null || form[key].length == 0) {
            return count;
        } else {
            return count + 1;
        }
    }, 0)
};

function handleReset() {
    form = reactive({
        page: 1,
        pagesize: 10,
        project_number: form.project_number,
    });
    update_table();
};

function onPageChange() {
    update_table();
};

function onFilter() {
    form.page = 1;
    form.pagesize = 10;
    update_table();
};

function handleAdd() {
    router.push({ path: '/test_plans_v2/add', query: { project_number: form.project_number } });
};

function handleEdit(row) {
    router.push({ path: '/test_plans_v2/add', query: { type: 'edit', id: row.id } });
};

function handleCopy(row) {
    r_id.value = row.id;
    dialogCopyVisible.value = true;
};

function onCopyConfirm() {
    dialogCopyVisible.value = false;
    update_table();
};

function handleDelete(row) {
    ElMessageBox.confirm(
        '确定删除吗?',
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        http.delete(`/v2/test_plans/${row.id}`).then(res => {
            ElMessage({
                message: '删除成功.',
                type: 'success',
            });
            update_table();
        }).catch(err => {
            ElMessageBox.alert(
                err.response.data.msg,
                '警告',
                {
                    confirmButtonText: '确定',
                    type: 'warning',
                }
            )
        });
    }).catch(() => {
        ElMessage({
            type: 'info',
            message: '已取消删除'
        });
    });
};

function onDetail(row) {
    r_id.value = row.id;
    drawerDetailVisible.value = true;
};

function handleUpdateStatus(row) {
    versionInfo.value = row;
    dialogVisible.value = true;
};

function onUpdateStatusConfirm() {
    dialogVisible.value = false;
    update_table();
};

function handleRefresh() {
    update_table();
};

function handleTestRecord(row) {
    testRecordStore.setTestPlanId(row.id);
    router.push({ path: "/test_records_v2/list" });
};

watch(() => projectStore.project_info, () => {
    form.project_number = projectStore.project_info.projectCode;
    update_table();
});

onActivated(() => {
    if (testPlanStore.softwareVersion) {
        form.m_version = testPlanStore.softwareVersion;
        testPlanStore.clearSoftwareVersion();
    }
    const plan_id = route.query.id;
    form.project_number = projectStore.project_info.projectCode;
    if (plan_id) {
        form.id = plan_id;
    } 
    update_table();
});

</script>


<style lang="scss" scoped>
.search-container {
    width: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
    height: 40px;
}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
    border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    margin-bottom: 10px;

    .el-input,
    .el-select {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
    }
}

.tool-bar-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    justify-items: center;

    .el-select {
        width: 500px;
        margin-left: 10px;
    }

}

.el-aside {
    width: 200px;
    height: 100% !important;

    margin-top: 20px;
}

.column-popper-title {
    border-bottom: 1px solid #ebeef5;
}

/* 自定义滚动条样式 */
.column-content::-webkit-scrollbar {
    width: 6px;
    /* 滚动条宽度 */
}

.column-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    /* 轨道背景色 */
    border-radius: 4px;
    /* 轨道圆角 */
}

/* 滚动条滑块默认样式（浅色） */
.column-content::-webkit-scrollbar-thumb {
    background: #e4e3e3;
    /* 浅色 */
    border-radius: 4px;
    /* 滑块圆角 */
}

/* 滚动条滑块悬停样式（深色） */
.column-content::-webkit-scrollbar-thumb:hover {
    background: #c7c7c7;
    /* 深色 */
}
</style>