<template>
   <!-- 提示弹框 -->
  <TipDialog v-if="showTipDialog" />
  <div v-else class="page-container" v-custom-loading="loading">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <!-- 左侧版本标识 -->
        <div class="version-info" >
          <el-tag type="info" effect="plain" :style="{ border: 'none'}">
            <el-icon style="color: #409eff;"><InfoFilled /></el-icon>
            版本: {{ SdkVersion || 'N/A' }}
          </el-tag>
        </div>
      </div>
      <div class="toolbar-right">
        <!-- 工作空间和分支 -->
        <div class="config-selects" >
          <el-form-item label="项目仓库:" class="toolbar-form-item" style="font-size: 14px; font-weight: 300; color: #666;">
            <el-select
                v-model="form.gitlab"
                class="input-field"
                placeholder="请选择或输入仓库地址"
                filterable
                allow-create
                clearable
                style="border: none; padding: 0px;"
                @visible-change="handleGitlabClick"
              >
                <el-option
                  v-for="item in spaceOptions"
                  :key="item"
                  :label="item"
                  :value="item"
                />
              </el-select>
          </el-form-item>
          <el-form-item label="工作空间:" class="toolbar-form-item" style="font-size: 14px; font-weight: 300; color: #666;">
            <el-select
                  v-model="form.project_branch"
                  class="input-field"
                  placeholder="请选择或输入分支"
                  filterable
                  allow-create
                  clearable
                  style="border: none; padding: 0px;"
                >
                  <el-option
                    v-for="branch in branchOptions"
                    :key="branch"
                    :label="branch"
                    :value="branch"
                  />
                </el-select>
          </el-form-item>
        </div>
        <!-- 功能按钮 -->
        <div class="button-group">
          <el-button type="primary" @click="handleSave">
            Commit
          </el-button>
          <el-button type="success" @click="handlePublish" >
            Push
          </el-button>
          <el-button type="danger" @click="mergetest" >
            Merge
          </el-button>
        </div>
      </div>
    </div>
    <!-- 弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      title="选择合并分支"
      width="400px"
      :show-close="true"
      center
      class="merge-dialog"
    >
      <div class="dialog-content">
        <p class="dialog-description">请选择要合入的目标分支</p>
        <el-form label-position="top">
          <el-form-item label="目标分支">
            <el-select v-model="merge_branch" placeholder="请选择分支" style="width: 100%">
              <el-option v-for="item in ['dev']" :key="item" :label="item" :value="item">
                {{ item }}
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false" >取消</el-button>
          <el-button type="primary" @click="confirmAction" >确定合并</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 主要内容区域 -->
    <div class="main-content" >
      <!-- 第二行：功能模块和配置详情 -->
      <div class="content-row">
        <!-- 功能模块区域 -->
        <div class="module-section">
          <div class="section-header">
            <div class="section-title">
              <el-icon class="section-icon"><Menu /></el-icon>
              <span>功能模块</span>
            </div>
          </div>
          <div class="module-content">
            <el-tree
              :data="treeData"
              :props="defaultProps"
              @node-click="handleNodeClick"
              node-key="label"
              highlight-current
              class="custom-tree"
              :expand-on-click-node="false"
              :default-expand-all="false"
            />
          </div>
        </div>

        <!-- 配置详情区域 -->
        <div class="config-section">
          <div class="section-header">
            <div class="section-title">
              <el-icon class="section-icon"><Tools /></el-icon>
              <span>配置详情</span>
            </div>
          </div>
          <div class="config-content">
            <div class="config-form" v-if="showTree">
              <Configuration
                :config="configData"
                :workspacePath="workspace"
                :branchStatus="branch_status"
              />
            </div>
            <!-- 空状态 -->
            <div class="empty-state" v-else>
              <el-empty description="请选择左侧功能模块进行配置">
                <template #image>
                  <el-icon size="64" color="#c0c4cc"><DocumentCopy /></el-icon>
                </template>
              </el-empty>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

 
</template>

<script setup>
import { ref, onMounted, watch, onActivated, onDeactivated, reactive } from 'vue';
import { useRoute } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import http from '@/utils/http/http';
import Configuration from '@/views/code_management/config/Configuration.vue';
import { useProjectStore } from '@/stores/project.js';
import TipDialog from '../index.vue';
import {
  InfoFilled,
  Folder,
  Monitor,
  Operation,
  Star,
  EditPen,
  Lightning,
  Menu,
  Tools,
  DocumentCopy
} from '@element-plus/icons-vue';

// 定义组件名称，用于本地存储键名
const componentName = 'configInfoConfig';

const route = useRoute();
const projectStore = useProjectStore();

// 界面状态数据
const workspace = ref('');
const branch_status = ref('');
const SdkVersion = ref('');
const showTree = ref(false);
const treeData = ref([]);
const defaultProps = { children: 'children', label: 'label' };
const configData = ref({});
const loading = ref(false);
const dialogVisible = ref(false);
const merge_branch = ref('dev');
const showTipDialog = ref(false);

// 项目相关变量
let project_code = '';
let project_name = '';
let currentMessage = null;
let pick_tree_info = null;
let workspace_path = '';

// 表单数据
const form = reactive({
  gitlab: '',
  project_branch: ''
});
const spaceOptions = ref([]);
const branchOptions = ref([]);

const chipForm = reactive({
  workspace: 'default', // 工作空间
  branchStatus: 'main', // 分支状态
});

// 单击仓库
const handleGitlabClick = (visible) => {
  // 判断有无存在的ElMessage， 存在则关闭
  if (!project_code) {
    if (currentMessage) {
      currentMessage.close();
    }
    currentMessage = ElMessage.warning('请选择项目！')
  }
}



onMounted(() => {
    // 从路由参数获取初始数据
    // 监控项目信息变化
    const info = projectStore.project_info || {};
    project_code = info.projectCode || '';
    project_name = info.name || '';

    console.log('project_code:', project_code);
    console.log('project_name:', project_name);

    if (project_code=="" && project_name=="") {
      console.info('项目信息为空，请先选择项目');
      showTipDialog.value = true;
    } else {
      console.info('项目信息已选择，开始初始化');
      showTipDialog.value = false;
      get_space();
    }


    // 监控项目信息变化
    watch(() => projectStore.project_info, (newval) => {
      if (newval) {
        project_code = newval.projectCode || '';
        project_name = newval.name || '';

        // 检查项目是否选择
        if (project_code=="" && project_name=="") {
          showTipDialog.value = true;
        } else {
          showTipDialog.value = false;
          // 清空仓库和分支
          form.gitlab = '';
          form.project_branch = '';
          get_space();
        }
      }
    });

    // 监控仓库信息变化
    watch(() => form.gitlab, ( newval, oldval) => {
      if (newval !== oldval) {
        form.project_branch = '';
        get_branch();
      }
    });

    // 监控分支信息变化
     watch(() => form.project_branch, ( newval, oldval) => {
      if (newval !== oldval) {
        submit_branch_info();
      }
    });

});



// 获取仓库列表
const get_space = async () => {
  try {
    loading.value = true;
    console.log('开始获取仓库信息，项目代码:', project_code);

    const response = await http.get('/code_management/space_options', {
      params: {
        project_code: project_code
      }
    });

    console.log("获取已创建的仓库信息:", response.data);

    if (response.data.status === 1) {
      spaceOptions.value = response.data.space_options || [];
      console.log('仓库列表:', spaceOptions.value);

      // 设置默认仓库为第一个
      if (spaceOptions.value.length > 0 && !form.gitlab) {
        form.gitlab = spaceOptions.value[0];
        console.log('设置默认仓库:', form.gitlab);

        // 自动获取分支信息
        await get_branch();
      } else if (spaceOptions.value.length === 0) {
        ElMessage.warning('该项目暂无可用仓库');
        showTipDialog.value = true;
      }
    } else {
      console.error('获取仓库信息失败:', response.data.message);
      ElMessage.error(response.data.message || '获取仓库信息失败');
    }
  } catch (error) {
    console.error('获取仓库信息失败:', error.message);
    ElMessage.error('获取仓库信息失败: ' + error.message);
  } finally {
    loading.value = false;
  }
};

// 获取分支列表
const get_branch = async () => {
  if (!form.gitlab) {
    console.warn('仓库地址为空，无法获取分支');
    return;
  }

  try {
    console.log('开始获取分支信息，仓库:', form.gitlab);

    const response = await http.get('/code_management/branch_options', {
      params: {
        project_code: project_code,
        project_gitlab: form.gitlab
      }
    });

    if (response.data.status === 1) {
      branchOptions.value = response.data.branch_options || [];
      console.log('分支列表:', branchOptions.value);

      // 设置默认分支为第一个
      if (branchOptions.value.length > 0 && !form.project_branch) {
        form.project_branch = branchOptions.value[0];
        console.log('设置默认分支:', form.project_branch);

        // 自动提交分支信息
        await submit_branch_info();
      } else if (branchOptions.value.length === 0) {
        ElMessage.warning('该仓库暂无可用分支');
        showTipDialog.value = true;
      }
    } else {
      console.error('获取分支信息失败:', response.data.message);
      ElMessage.error(response.data.message || '获取分支信息失败');
    }
  } catch (error) {
    console.error('获取分支信息失败:', error.message);
    ElMessage.error('获取分支信息失败: ' + error.message);
  }
};

// 提交分支信息并获取SDK版本具体信息
const submit_branch_info = async () => {
  if (!form.gitlab || !form.project_branch) {
    console.warn('仓库或分支信息不完整，无法提交');
    return;
  }

  try {
    loading.value = true;
    console.log('提交分支信息:', {
      project_code,
      project_name,
      gitlab: form.gitlab,
      branch: form.project_branch
    });

    const response = await http.get('/code_management/branch_submit', {
      params: {
        project_code: project_code,
        project_name: project_name,
        project_gitlab: form.gitlab,
        project_branch: form.project_branch
      },
      timeout: 60000
    });

    console.log("分支提交响应:", response.data);

    if (response.data.config_status === 1) {
      treeData.value = response.data.pick_tree_info || [];
      workspace_path = response.data.work_space;
      workspace.value = response.data.work_space;
      branch_status.value = response.data.branch_status;
      SdkVersion.value = response.data.sdk_version;

      console.log('SDK版本:', SdkVersion.value);
      console.log('树形数据:', treeData.value);
      console.log('工作空间:', workspace.value);
      console.log('分支状态:', branch_status.value);

      // 可以在这里添加其他成功后的操作
      if (currentMessage) {
      currentMessage.close();
    }
    currentMessage =  ElMessage.success('项目配置加载成功');
    } else {
      const errorMsg = response.data.message || '分支提交失败';
      console.error('分支提交失败:', errorMsg);
      ElMessage.error(errorMsg);
    }
  } catch (error) {
    console.error('分支提交失败:', error.message);
    ElMessage.error('分支提交失败: ' + error.message);
  } finally {
    loading.value = false;
  }
};



// 左侧目录树点击事件
const handleNodeClick = (node) => {
  const isLeaf = !node.children || node.children.length === 0;

  showTree.value = false;
  if (isLeaf && node.label === "Config_Brightness") {

    const parentName = findTopLevelParentLabel(treeData.value, node.label);
    
    http.post('/code_management/configuration_info', {
      params: { nodeName: node.label, parentName, workspace_path: workspace.value, branch_status: branch_status.value }
    }).then(response => {
      if (response.data.config_status === 1) {
        if (isLeaf && node.label === "Config_Brightness") {
          configData.value = response.data.config_info.Config_Brightness;
        }
        showTree.value = true;
      } else {
        if (currentMessage) {
          currentMessage.close();
        }
        currentMessage = ElMessage.error('配置详情获取失败');
      }
    }).catch(() => {
      if (currentMessage) {
        currentMessage.close();
      }
      currentMessage = ElMessage.error('无法获取配置详情');
    });
  }


};

// 提取节点顶层父标签
const findTopLevelParentLabel = (tree, targetLabel, path = []) => {
  if (!tree || !Array.isArray(tree)) {
    console.warn('树数据无效:', tree);
    return null;
  }

  for (const node of tree) {
    if (!node || !node.label) {
      console.warn('节点数据无效:', node);
      continue;
    }

    const currentPath = [...path, node.label];
    if (node.label === targetLabel) return currentPath[0];
    if (node.children) {
      const result = findTopLevelParentLabel(node.children, targetLabel, currentPath);
      if (result) return result;
    }
  }
  return null;
};

// commit 操作
const handleSave = async () => {
  ElMessageBox.prompt('请输入 commit 信息', '保存配置', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    inputErrorMessage: '请输入 commit 信息',
    inputValidator: (value) => value != null && value.trim() !== ''
  }).then(({ value }) => {
    loading.value = true;
    http.post('/code_management/config_commit', {
      params: { commit_message: value, workspace_path: workspace.value, branch_status: branch_status.value },
      timeout: 60000
    }).then(response => {
      loading.value = false;
      if (response.data.commit_status === 1) {
        ElMessage.success('配置已保存:commit成功');
      } else {
        ElMessage.error('配置保存失败:commit失败');
      }
    }).catch(() => {
      loading.value = false;
      ElMessage.error('保存失败');
    });
  }).catch(() => {
    ElMessage.info('取消保存');
  });
};

// push 操作
const handlePublish = () => {
  ElMessageBox.confirm(
    '确定要发布当前配置吗？发布后将生效并覆盖现有配置。',
    '发布确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    loading.value = true;
    http.post('/code_management/config_push', {
      params: { workspace_path: workspace.value, branch_status: branch_status.value },
      timeout: 60000
    }).then(response => {
      loading.value = false;
      if (response.data.push_status === 1) {
        ElMessage.success('配置已发布');
        dialogVisible.value = true;
      } else {
        ElMessage.error('配置发布失败');
      }
    }).catch(() => {
      loading.value = false;
      ElMessage.info('已取消发布');
    });
  }).catch(() => {
    ElMessage.info('已取消发布');
  });
};

// merge 确认操作
const confirmAction = () => {
  ElMessageBox.confirm(
    '确定要merge当前分支到目标分支吗？',
    '发布确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    loading.value = true;
    http.post('/code_management/merge_project', {
      params: { workspace_path: workspace.value, merge_branch: merge_branch.value },
      timeout: 60000
    }).then(() => {
      ElMessage.success('已发送merge信息,请等待管理员审批');
      dialogVisible.value = false;
      loading.value = false;
    }).catch(() => {
      ElMessage.error('merge失败');
      loading.value = false;
    });
  }).catch(() => {
    ElMessage.info('已取消merge');
  });
};

// 测试merge功能（打开弹窗）
const mergetest = () => {
  dialogVisible.value = true;
};




</script>

<style scoped>
/* 树形菜单样式优化 */
:deep(.el-tree) {
  --level-indent: 20px;
  --level-color-1: #333;
  --level-color-2: #666;
  --level-color-3: #999;
  background-color: transparent;
  font-size: 14px;
}

:deep(.el-tree-node__content) {
  height: 50px;
  transition: all 0.3s;
  border-radius: 4px;
  margin-bottom: 6px;
  padding: 0 8px;
}

:deep(.el-tree-node__content:hover) {
  background-color: #409eff22;
  color: #666;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color:	#409eff;
  color: #FFF;
}

:deep(.el-tree-node__expand-icon) {
  color: #666;
  font-size: 12px;
}



/* 主要内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 0;
  gap: 0;
}

/* 第二行：功能模块和配置详情 */
.content-row {
  flex: 1;
  display: grid;
  grid-template-columns: 350px 1fr;
  gap: 20px;
  padding: 20px 2px;
  overflow: visible;
}

/* 功能模块区域 */
.module-section {
  background-color: #fff;
  border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
}

/* 配置详情区域 */
.config-section {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
}

/* 区域头部样式 */
.section-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e4e7ed;
  background: #fff;
}

/* 功能模块头部 */
.module-section .section-header {
  border-radius: 12px;
}

/* 配置详情头部 */
.config-section .section-header {
  border-radius: 12px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #666;
  margin: 0;
}

.section-icon {
  font-size: 18px;
  color: #409eff;
}

/* 侧边栏头部 */
.sidebar-header {
  border-radius: 8px;
  padding: 20px;
  border-bottom: 1px solid #e4e7ed;
  /* background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); */
}

.module-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin: 0;
}

.module-icon {
  font-size: 18px;
}

/* 模块内容区域 */
.module-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

/* 配置内容区域 */
.config-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.config-form {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

/* 空状态 */
.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}





.version-info {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #999;
  font-weight: 500;
}

/* 页面容器 */
.page-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  /* margin: 20px; */
}

/* 顶部工具栏 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 30px;
  height: 50px;
  background: #fff;
  color: white;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
  border-radius: 8px;
  /* margin: 0px 20px; */
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
}

.title-icon {
  font-size: 20px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.config-selects {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 14px;
  font-weight: normal;
}


.toolbar-form-item {
  margin-bottom: 0 !important;
  display: flex;
  align-items: center;
  gap: 8px;
}

.toolbar-form-item .el-form-item__label {
  margin-bottom: 0;
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
  font-weight: normal;
}

.toolbar-form-item .el-form-item__content {
  margin-left: 0 !important;
}

.toolbar-form-item .el-select {
  width: 140px;
}

.button-group {
  display: flex;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .toolbar-right {
    gap: 15px;
  }

  .config-selects {
    gap: 10px;
  }

  .toolbar-form-item .el-select {
    width: 120px;
  }
}

@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .toolbar-right {
    width: 100%;
    justify-content: space-between;
  }

  .config-selects {
    flex-wrap: wrap;
    gap: 8px;
  }

  .toolbar-form-item .el-select {
    width: 100px;
  }
}




</style>