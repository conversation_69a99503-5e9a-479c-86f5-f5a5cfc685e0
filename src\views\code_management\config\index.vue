<template>
  <!-- 提示弹框 -->
  <TipDialog v-if="showTipDialog" />
  <div v-else class="page-container" v-custom-loading="loading">
    <!-- 顶部工具栏组件 -->
    <Toolbar
      :sdk-version="SdkVersion"
      :form="form"
      :space-options="spaceOptions"
      :branch-options="branchOptions"
      :loading="loading"
      @update:form="(newForm) => Object.assign(form, newForm)"
      @gitlab-click="handleGitlabClick"
      @save="handleSave"
      @publish="handlePublish"
      @merge-test="mergetest"
      @confirm-action="confirmAction"
    />

    <!-- 主要内容区域 -->
    <div class="main-content" >
      <!-- 第二行：功能模块和配置详情 -->
      <div class="content-row">
        <!-- 功能模块区域 -->
        <div class="module-section">
          <div class="section-header">
            <div class="section-title">
              <el-icon class="section-icon"><Menu /></el-icon>
              <span>功能模块</span>
            </div>
          </div>
          <div class="module-content">
            <el-tree
              :data="treeData"
              :props="defaultProps"
              @node-click="handleNodeClick"
              node-key="label"
              highlight-current
              class="custom-tree"
              :expand-on-click-node="false"
              :default-expand-all="false"
            />
          </div>
        </div>

        <!-- 配置详情区域 -->
        <div class="config-section">
          <div class="section-header">
            <div class="section-title">
              <el-icon class="section-icon"><Tools /></el-icon>
              <span>配置详情</span>
            </div>
          </div>
          <div class="config-content">
            <div class="config-form" v-if="showTree">
              <Configuration
                :config="configData"
                :workspacePath="workspace"
                :branchStatus="branch_status"
              />
            </div>
            <!-- 空状态 -->
            <div class="empty-state" v-else>
              <el-empty description="请选择左侧功能模块进行配置">
                <template #image>
                  <el-icon size="64" color="#c0c4cc"><DocumentCopy /></el-icon>
                </template>
              </el-empty>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

 
</template>

<script setup>
import { ref, onMounted, watch, onActivated, onDeactivated, reactive } from 'vue';
import { useRoute } from 'vue-router';
import { ElMessageBox } from 'element-plus';
import http from '@/utils/http/http';
import messageManager from '@/utils/messageManager';
import Configuration from '@/views/code_management/config/Configuration.vue';
import Toolbar from '@/views/code_management/Toolbar.vue';
import { useProjectStore } from '@/stores/project.js';
import TipDialog from '../None.vue';
import {
  Folder,
  Monitor,
  Operation,
  Star,
  EditPen,
  Lightning,
  Menu,
  Tools,
  DocumentCopy
} from '@element-plus/icons-vue';

// 定义组件名称，用于本地存储键名
const componentName = 'configInfoConfig';

const route = useRoute();
const projectStore = useProjectStore();

// 界面状态数据
const workspace = ref('');
const branch_status = ref('');
const SdkVersion = ref('');
const showTree = ref(false);
const treeData = ref([]);
const defaultProps = { children: 'children', label: 'label' };
const configData = ref({});
const loading = ref(false);
const showTipDialog = ref(false);

// 项目相关变量
let project_code = '';
let project_name = '';
let pick_tree_info = null;
let workspace_path = '';

// 表单数据
const form = reactive({
  gitlab: '',
  project_branch: ''
});
const spaceOptions = ref([]);
const branchOptions = ref([]);

const chipForm = reactive({
  workspace: 'default', // 工作空间
  branchStatus: 'main', // 分支状态
});

// 单击仓库
const handleGitlabClick = (visible) => {
  // 判断有无存在的ElMessage， 存在则关闭
  if (!project_code) {
    messageManager.warning('请选择项目！')
  }
}



onMounted(() => {
      try {
    // 监控项目信息变化
    const info = projectStore.project_info || {};
    project_code = info.projectCode || '';
    project_name = info.name || '';

    console.log('project_code:', project_code);
    console.log('project_name:', project_name);

    if (project_code=="" && project_name=="") {
      console.info('项目信息为空，请先选择项目');
      showTipDialog.value = true;
    } else {
      console.info('项目信息已选择，开始初始化');
      showTipDialog.value = false;
      get_space();
    }
    // 获取gpio芯片信息及引脚信息
    initTableData();

    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize);

  } catch (error) {
    console.error('组件初始化错误:', error);
    messageManager.error('组件初始化失败，请刷新页面重试');
  }
});


    // 监控项目信息变化
    watch(() => projectStore.project_info, (newval, oldval) => {
      if (newval) {
        const newProjectCode = newval.projectCode || '';
        const newProjectName = newval.name || '';

        // 只有当项目代码真正发生变化时才重新获取仓库信息
        if (newProjectCode !== project_code) {
          console.log('项目切换:', project_code, '->', newProjectCode);

          project_code = newProjectCode;
          project_name = newProjectName;

          // 检查项目是否选择
          if (project_code=="" && project_name=="") {
            showTipDialog.value = true;
          } else {
            showTipDialog.value = false;
            // 清空仓库和分支
            form.gitlab = '';
            form.project_branch = '';
            get_space();
          }
        }
      }
    });

        // 监控仓库信息变化
    watch(() => form.gitlab, ( newval, oldval) => {
      if (newval !== oldval) {
        console.log('👀 watch监听器触发 - 仓库变化:', { oldval, newval });
        form.project_branch = '';
        get_branch();
      }
    });

    // 监控分支信息变化
     watch(() => form.project_branch, ( newval, oldval) => {
      if (newval !== oldval) {
        console.log('👀 watch监听器触发 - 分支变化:', { oldval, newval });
        submit_branch_info();
      }
    });


    
// 获取仓库列表
const get_space = async () => {
  try {
    loading.value = true;
    console.log('开始获取仓库信息，项目代码:', project_code);

    const response = await http.get('/code_management/space_options', {
      params: {
        project_code: project_code
      }
    });

    console.log("获取已创建的仓库信息:", response.data);

    if (response.data.status === 1) {
      spaceOptions.value = response.data.space_options || [];
      console.log('仓库列表:', spaceOptions.value);

      // 设置默认仓库为第一个
      if (spaceOptions.value.length > 0 && !form.gitlab) {
        form.gitlab = spaceOptions.value[0];
        console.log('🔧 get_space 设置默认仓库:', form.gitlab, '- watch监听器将自动触发get_branch');
        // 移除手动调用，依赖watch监听器自动触发get_branch
      } else if (spaceOptions.value.length === 0) {
        messageManager.warning('该项目暂无可用仓库');
        showTipDialog.value = true;
      }
    } else {
      console.error('获取仓库信息失败:', response.data.message);
      messageManager.error(response.data.message || '获取仓库信息失败');
    }
  } catch (error) {
    console.error('获取仓库信息失败:', error.message);
    messageManager.error('获取仓库信息失败: ' + error.message);
  } finally {
    loading.value = false;
  }
};

// 获取分支列表
const get_branch = async () => {
  if (!form.gitlab) {
    console.warn('仓库地址为空，无法获取分支');
    return;
  }

  try {
    console.log('🔄 get_branch 被调用 - 调用堆栈:', new Error().stack);
    console.log('开始获取分支信息，仓库:', form.gitlab);

    const response = await http.get('/code_management/branch_options', {
      params: {
        project_code: project_code,
        project_gitlab: form.gitlab
      }
    });

    if (response.data.status === 1) {
      branchOptions.value = response.data.branch_options || [];
      console.log('分支列表:', branchOptions.value);

      // 设置默认分支为第一个
      if (branchOptions.value.length > 0 && !form.project_branch) {
        form.project_branch = branchOptions.value[0];
        console.log('🔧 get_branch 设置默认分支:', form.project_branch, '- watch监听器将自动触发submit_branch_info');
        // 移除手动调用，依赖watch监听器自动触发submit_branch_info
      } else if (branchOptions.value.length === 0) {
        messageManager.warning('该仓库暂无可用分支');
        showTipDialog.value = true;
      }
    } else {
      console.error('获取分支信息失败:', response.data.message);
      messageManager.error(response.data.message || '获取分支信息失败');
    }
  } catch (error) {
    console.error('获取分支信息失败:', error.message);
    messageManager.error('获取分支信息失败: ' + error.message);
  }
};

// 提交分支信息并获取SDK版本具体信息
const submit_branch_info = async () => {
  if (!form.gitlab || !form.project_branch) {
    console.warn('仓库或分支信息不完整，无法提交');
    return;
  }

  try {
    loading.value = true;
    console.log('🔄 submit_branch_info 被调用 - 调用堆栈:', new Error().stack);
    console.log('提交分支信息:', {
      project_code,
      project_name,
      gitlab: form.gitlab,
      branch: form.project_branch
    });

    const response = await http.get('/code_management/branch_submit', {
      params: {
        project_code: project_code,
        project_name: project_name,
        project_gitlab: form.gitlab,
        project_branch: form.project_branch
      },
      timeout: 60000
    });

    console.log("分支提交响应:", response.data);

    if (response.data.config_status === 1) {
      treeData.value = response.data.pick_tree_info || [];
      workspace_path = response.data.work_space;
      workspace.value = response.data.work_space;
      branch_status.value = response.data.branch_status;
      SdkVersion.value = response.data.sdk_version;

      console.log('SDK版本:', SdkVersion.value);
      console.log('树形数据:', treeData.value);
      console.log('工作空间:', workspace.value);
      console.log('分支状态:', branch_status.value);

      // 可以在这里添加其他成功后的操作
      messageManager.success('项目配置加载成功');
    } else {
      const errorMsg = response.data.message || '分支提交失败';
      console.error('分支提交失败:', errorMsg);
      messageManager.error(errorMsg);
    }
  } catch (error) {
    console.error('分支提交失败:', error.message);
    messageManager.error('分支提交失败: ' + error.message);
  } finally {
    loading.value = false;
  }
};



// 左侧目录树点击事件
const handleNodeClick = (node) => {
  const isLeaf = !node.children || node.children.length === 0;

  showTree.value = false;
  if (isLeaf && node.label === "Config_Brightness") {

    const parentName = findTopLevelParentLabel(treeData.value, node.label);
    
    http.post('/code_management/configuration_info', {
      params: { nodeName: node.label, parentName, workspace_path: workspace.value, branch_status: branch_status.value }
    }).then(response => {
      if (response.data.config_status === 1) {
        if (isLeaf && node.label === "Config_Brightness") {
          configData.value = response.data.config_info.Config_Brightness;
        }
        showTree.value = true;
      } else {
        messageManager.error('配置详情获取失败');
      }
    }).catch(() => {
      messageManager.error('无法获取配置详情');
    });
  }


};

// 提取节点顶层父标签
const findTopLevelParentLabel = (tree, targetLabel, path = []) => {
  if (!tree || !Array.isArray(tree)) {
    console.warn('树数据无效:', tree);
    return null;
  }

  for (const node of tree) {
    if (!node || !node.label) {
      console.warn('节点数据无效:', node);
      continue;
    }

    const currentPath = [...path, node.label];
    if (node.label === targetLabel) return currentPath[0];
    if (node.children) {
      const result = findTopLevelParentLabel(node.children, targetLabel, currentPath);
      if (result) return result;
    }
  }
  return null;
};

// commit 操作
const handleSave = async () => {
  ElMessageBox.prompt('请输入 commit 信息', '保存配置', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    inputErrorMessage: '请输入 commit 信息',
    inputValidator: (value) => value != null && value.trim() !== ''
  }).then(({ value }) => {
    loading.value = true;
    http.post('/code_management/config_commit', {
      params: { commit_message: value, workspace_path: workspace.value, branch_status: branch_status.value },
      timeout: 60000
    }).then(response => {
      loading.value = false;
      if (response.data.commit_status === 1) {
        messageManager.success('配置已保存:commit成功');
      } else {
        messageManager.error('配置保存失败:commit失败');
      }
    }).catch(() => {
      loading.value = false;
      messageManager.error('保存失败');
    });
  }).catch(() => {
    messageManager.info('取消保存');
  });
};

// push 操作
const handlePublish = () => {
  ElMessageBox.confirm(
    '确定要发布当前配置吗？发布后将生效并覆盖现有配置。',
    '发布确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    loading.value = true;
    http.post('/code_management/config_push', {
      params: { workspace_path: workspace.value, branch_status: branch_status.value },
      timeout: 60000
    }).then(response => {
      loading.value = false;
      if (response.data.push_status === 1) {
        messageManager.success('配置已发布');
      } else {
        messageManager.error('配置发布失败');
      }
    }).catch(() => {
      loading.value = false;
      messageManager.info('已取消发布');
    });
  }).catch(() => {
    messageManager.info('已取消发布');
  });
};

// merge 确认操作
const confirmAction = (mergeBranch) => {
  ElMessageBox.confirm(
    '确定要merge当前分支到目标分支吗？',
    '发布确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    loading.value = true;
    http.post('/code_management/merge_project', {
      params: { workspace_path: workspace.value, merge_branch: mergeBranch },
      timeout: 60000
    }).then(() => {
      messageManager.success('已发送merge信息,请等待管理员审批');
      loading.value = false;
    }).catch(() => {
      messageManager.error('merge失败');
      loading.value = false;
    });
  }).catch(() => {
    messageManager.info('已取消merge');
  });
};

// 测试merge功能（由工具栏组件处理弹窗）
const mergetest = () => {
  // 弹窗逻辑现在在Toolbar组件中处理
  console.log('准备合并操作');
};




</script>

<style scoped>
/* 树形菜单样式优化 */
:deep(.el-tree) {
  --level-indent: 20px;
  --level-color-1: #333;
  --level-color-2: #666;
  --level-color-3: #999;
  background-color: transparent;
  font-size: 14px;
}

:deep(.el-tree-node__content) {
  height: 50px;
  transition: all 0.3s;
  border-radius: 4px;
  margin-bottom: 6px;
  padding: 0 8px;
}

:deep(.el-tree-node__content:hover) {
  background-color: #409eff22;
  color: #666;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color:	#409eff;
  color: #FFF;
}

:deep(.el-tree-node__expand-icon) {
  color: #666;
  font-size: 12px;
}



/* 主要内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 0;
  gap: 20;
}

/* 第二行：功能模块和配置详情 */
.content-row {
  flex: 1;
  display: grid;
  grid-template-columns: 350px 1fr;
  gap: 20px;
  padding: 20px 2px;
  overflow: visible;
}

/* 功能模块区域 */
.module-section {
  background-color: #fff;
  border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
}

/* 配置详情区域 */
.config-section {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
}

/* 区域头部样式 */
.section-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e4e7ed;
  background: #fff;
}

/* 功能模块头部 */
.module-section .section-header {
  border-radius: 12px;
}

/* 配置详情头部 */
.config-section .section-header {
  border-radius: 12px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #666;
  margin: 0;
}

.section-icon {
  font-size: 18px;
  color: #409eff;
}

/* 侧边栏头部 */
.sidebar-header {
  border-radius: 8px;
  padding: 20px;
  border-bottom: 1px solid #e4e7ed;
  /* background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); */
}

.module-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin: 0;
}

.module-icon {
  font-size: 18px;
}

/* 模块内容区域 */
.module-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

/* 配置内容区域 */
.config-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.config-form {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

/* 空状态 */
.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}





.version-info {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #999;
  font-weight: 500;
}

/* 页面容器 */
.page-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  /* margin: 20px; */
}

/* 工具栏样式现在在Toolbar组件中 */






</style>