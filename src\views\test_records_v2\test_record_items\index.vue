<template>

    <div class="n-container">
        <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/test_records' }">测试记录</el-breadcrumb-item>
            <el-breadcrumb-item>记录项</el-breadcrumb-item>
        </el-breadcrumb>

        <el-button size="small" @click="onBack">返回</el-button>
    </div>

    <div class="tool-bar-container">

        <div style="margin-left: auto; display: flex; gap: 10px;">
            <el-tooltip class="box-item" effect="dark" content="重置已添加的筛选条件" placement="top-start">
                <el-button text bg @click="handleReset">重置</el-button>
            </el-tooltip>
            <filterButton @click="onFilterStatusChange" :count="filterCount" />
            <el-button icon="Refresh" text bg @click="handleRefresh">刷新</el-button>
        </div>
    </div>

    <div class="filter-container" v-if="showFilterContainer">
        <el-input v-model="form.test_case_name_re" size="large" placeholder="请输入测试项名称" @keyup.enter="onFilter" clearable>
            <template #append>
                <el-button icon="Search" @click="onFilter"></el-button>
            </template>
        </el-input>
        <el-select v-model="form.result_list" placeholder="请选择测试结果" @change="onFilter" style="width: 400px;" multiple
            clearable>
            <el-option label="待判定" value="2"></el-option>
            <el-option label="PASS" value="1"></el-option>
            <el-option label="NG" value="0"></el-option>
        </el-select>
        <el-select v-model="form.status_list" placeholder="请选择处理状态" @change="onFilter" style="width: 400px;" multiple
            clearable>
            <el-option label="未处理" value="0"></el-option>
            <el-option label="已处理" value="1"></el-option>
        </el-select>
    </div>

    <el-table :data="tableData" stripe border style="width: 100%" class="table-container">

        <el-table-column prop="test_case_number" label="测试用例ID" width="200" align="center"></el-table-column>
        <el-table-column prop="test_case_name" label="测试项名称" width="200" align="center"></el-table-column>
        <el-table-column prop="result" label="测试结果" width="100" align="center">
            <template #default="{ row }">
                <el-tag v-if="row.result === null" type="warning">待判定</el-tag>
                <el-tag v-else-if="row.result" type="success">PASS</el-tag>
                <el-tag v-else="row.result == 1" type="danger">NG</el-tag>
            </template>
        </el-table-column>
        <el-table-column label="处理状态" width="100" align="center">
            <template #default="{ row }">
                <el-tag v-if="row.status == 0" type="warning">未处理</el-tag>
                <el-tag v-if="row.status == 1" type="success">已处理</el-tag>
            </template>
        </el-table-column>
        <el-table-column prop="tester_name" label="测试人" width="100" align="center"></el-table-column>
        <el-table-column prop="project_name" label="所属项目" width="200" align="center"></el-table-column>
        <el-table-column prop="project_number" label="项目编号" width="150" align="center"></el-table-column>
        <el-table-column prop="test_plan_name" label="计划名称" width="200" align="center"></el-table-column>
        <el-table-column prop="start_time" label="开始时间" width="200" align="center"></el-table-column>
        <el-table-column prop="end_time" label="结束时间" width="200" align="center"></el-table-column>
        <el-table-column label="操作" min-width="100" fixed="right" align="center">
            <template #default="{ row, $index }">
                <el-button type="primary" size="small" @click="handledetail(row, $index)">详情</el-button>
            </template>
        </el-table-column>

    </el-table>

    <div class="pagination-container">
        <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100]" layout="prev, pager, next, jumper, total, sizes"
            :total="total" v-model:current-page="form.page" v-model:page-size="form.pagesize" background
            @change="onPageChange" />
    </div>

</template>

<script setup>

import { ref, reactive, onMounted, onActivated } from 'vue';
import http from '@/utils/http/http.js';
import { useRouter, useRoute } from 'vue-router';
import filterButton from '@/components/filterButton.vue';

const router = useRouter();
const route = useRoute();
const projects = ref([]);
const tableData = ref([]);
const count = ref(0);
const filterCount = ref(0);

const props = defineProps(
    {
        id: {
            type: String,
            default: ''
        }
    }
);

let form = reactive({
    page: 1,
    pagesize: 10,
    test_record_id: props.id ? props.id : route.query.id,
    test_case_id: route.query.test_case_id || null, 
});

let total = ref(0);

let showFilterContainer = ref(false);

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
};

function update_table() {
    http.get('/v2/test_records/items', { params: form }).then(res => {
        tableData.value = res.data.data.results;
        total.value = res.data.data.count;
        count.value = res.data.data.count;
    });

    filterCount.value = Object.keys(form).filter(key => !['page', 'pagesize', 'test_record_id', 'test_case_id'].includes(key)).reduce((count, key) => {
        if (form[key] == '' || form[key] == undefined || form[key] == null || form[key].length == 0) {
            return count;
        } else {
            return count + 1;
        }
    }, 0)
};

function handleReset() {
    form = reactive({
        page: 1,
        pagesize: 10,
        test_record_id: form.test_record_id,
    });
    update_table();
};

function onPageChange() {
    update_table();
};

function onFilter() {
    form.page = 1;
    form.pagesize = 10;
    update_table();
};

function handledetail(row, index) {
    router.push({ path: `/test_records_v2/items/${row.id}`, query: { q: JSON.stringify(form), count: count.value, current: (form.page - 1) * form.pagesize + index + 1 } });
};

function onBack() {
    router.push({path: '/test_records_v2'});
};

function handleRefresh() {
    update_table();
};

onActivated(() => {
    update_table();
});

</script>


<style lang="scss" scoped>
.search-container {
    width: 100%;
    display: flex;
    justify-content: center;
    justify-items: center;
    height: 40px;
}

.search-input {
    width: 50%;
    max-width: 500px;
}

.search-button {
    border: 1px solid #dcdfe6;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    margin-bottom: 10px;

    .el-input,
    .el-select {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
        height: 35px;
    }

    :deep(.el-select__wrapper) {
        height: 100%;
    }
}

.tool-bar-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    justify-items: center;

    .el-select {
        width: 500px;
    }

}

.el-aside {
    width: 200px;
    height: 100% !important;

    margin-top: 20px;
}

.el-main {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);

    margin-top: 0px;

    .table-container {
        flex: 1;
        overflow: auto;
    }
}

.n-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}
</style>