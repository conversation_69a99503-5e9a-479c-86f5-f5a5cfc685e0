<template>
  <div class="test-message-container">
    <h2>ElMessage管理器测试页面</h2>
    <div class="button-group">
      <el-button type="success" @click="showSuccess">显示成功消息</el-button>
      <el-button type="danger" @click="showError">显示错误消息</el-button>
      <el-button type="warning" @click="showWarning">显示警告消息</el-button>
      <el-button type="info" @click="showInfo">显示信息消息</el-button>
      <el-button @click="showMultiple">快速显示多条消息</el-button>
      <el-button @click="clearMessage">清除当前消息</el-button>
    </div>
    <div class="description">
      <p>这个页面用于测试消息管理器的功能：</p>
      <ul>
        <li>每次只显示一条消息</li>
        <li>新消息会自动清除历史消息</li>
        <li>可以手动清除当前消息</li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ElButton } from 'element-plus';
import messageManager from '@/utils/messageManager';

const showSuccess = () => {
  messageManager.success('这是一条成功消息！');
};

const showError = () => {
  messageManager.error('这是一条错误消息！');
};

const showWarning = () => {
  messageManager.warning('这是一条警告消息！');
};

const showInfo = () => {
  messageManager.info('这是一条信息消息！');
};

const showMultiple = () => {
  // 快速显示多条消息，测试是否只显示最后一条
  setTimeout(() => messageManager.success('第一条消息'), 100);
  setTimeout(() => messageManager.warning('第二条消息'), 200);
  setTimeout(() => messageManager.error('第三条消息（应该只显示这条）'), 300);
};

const clearMessage = () => {
  messageManager.clear();
};
</script>

<style scoped>
.test-message-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.button-group {
  margin: 20px 0;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.description {
  margin-top: 30px;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.description ul {
  margin: 10px 0;
  padding-left: 20px;
}

.description li {
  margin: 5px 0;
}
</style>
