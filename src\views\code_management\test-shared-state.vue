<template>
  <div class="test-container">
    <h2>代码管理状态共享测试</h2>
    
    <div class="section">
      <h3>项目信息</h3>
      <p>项目代码: {{ projectStore.project_info?.projectCode || '未选择' }}</p>
      <p>项目名称: {{ projectStore.project_info?.name || '未选择' }}</p>
    </div>

    <div class="section">
      <h3>代码管理状态</h3>
      <div class="state-info">
        <p><strong>当前仓库:</strong> {{ projectStore.codeManagement.gitlab || '未选择' }}</p>
        <p><strong>当前分支:</strong> {{ projectStore.codeManagement.project_branch || '未选择' }}</p>
        <p><strong>SDK版本:</strong> {{ projectStore.codeManagement.sdkVersion || '未获取' }}</p>
        <p><strong>仓库选项数量:</strong> {{ projectStore.codeManagement.spaceOptions.length }}</p>
        <p><strong>分支选项数量:</strong> {{ projectStore.codeManagement.branchOptions.length }}</p>
      </div>
    </div>

    <div class="section">
      <h3>仓库选项列表</h3>
      <ul v-if="projectStore.codeManagement.spaceOptions.length > 0">
        <li v-for="(option, index) in projectStore.codeManagement.spaceOptions" :key="index">
          {{ option }}
        </li>
      </ul>
      <p v-else>暂无仓库选项</p>
    </div>

    <div class="section">
      <h3>分支选项列表</h3>
      <ul v-if="projectStore.codeManagement.branchOptions.length > 0">
        <li v-for="(option, index) in projectStore.codeManagement.branchOptions" :key="index">
          {{ option }}
        </li>
      </ul>
      <p v-else>暂无分支选项</p>
    </div>

    <div class="section">
      <h3>操作按钮</h3>
      <div class="button-group">
        <el-button @click="clearState" type="danger">清空代码管理状态</el-button>
        <el-button @click="refreshState" type="primary">刷新状态显示</el-button>
        <el-button @click="goToConfig" type="success">跳转到配置页面</el-button>
        <el-button @click="goToGpio" type="success">跳转到GPIO页面</el-button>
      </div>
    </div>

    <div class="section">
      <h3>状态变化日志</h3>
      <div class="log-container">
        <div v-for="(log, index) in logs" :key="index" class="log-item">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useProjectStore } from '@/stores/project';

const router = useRouter();
const projectStore = useProjectStore();
const logs = ref([]);

// 添加日志
const addLog = (message) => {
  logs.value.unshift({
    time: new Date().toLocaleTimeString(),
    message
  });
  // 只保留最近20条日志
  if (logs.value.length > 20) {
    logs.value = logs.value.slice(0, 20);
  }
};

// 监听项目信息变化
watch(() => projectStore.project_info, (newVal, oldVal) => {
  if (newVal !== oldVal) {
    addLog(`项目信息变化: ${JSON.stringify(newVal)}`);
  }
}, { deep: true });

// 监听代码管理状态变化
watch(() => projectStore.codeManagement, (newVal, oldVal) => {
  if (newVal !== oldVal) {
    addLog(`代码管理状态变化: gitlab=${newVal.gitlab}, branch=${newVal.project_branch}, sdk=${newVal.sdkVersion}`);
  }
}, { deep: true });

// 清空状态
const clearState = () => {
  projectStore.clearCodeManagement();
  addLog('已清空代码管理状态');
};

// 刷新状态显示
const refreshState = () => {
  addLog('状态显示已刷新');
};

// 跳转到配置页面
const goToConfig = () => {
  router.push('/code_management/config');
};

// 跳转到GPIO页面
const goToGpio = () => {
  router.push('/code_management/gpio');
};

onMounted(() => {
  addLog('测试页面已加载');
});
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fff;
}

.section h3 {
  margin-top: 0;
  color: #303133;
  border-bottom: 2px solid #409eff;
  padding-bottom: 10px;
}

.state-info p {
  margin: 8px 0;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  background: #f8f9fa;
}

.log-item {
  display: flex;
  margin-bottom: 5px;
  font-size: 12px;
}

.log-time {
  color: #909399;
  margin-right: 10px;
  min-width: 80px;
}

.log-message {
  color: #606266;
  flex: 1;
}

ul {
  margin: 0;
  padding-left: 20px;
}

li {
  margin: 5px 0;
  padding: 5px;
  background: #f0f9ff;
  border-radius: 3px;
}
</style>
