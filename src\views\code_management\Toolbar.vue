<template>
  <!-- 顶部工具栏 -->
  <div class="toolbar">
    <div class="toolbar-left">
      <!-- 左侧版本标识 -->
      <div class="version-info">
        <el-tag type="info" effect="plain" :style="{ border: 'none'}">
          <el-icon style="color: #409eff;"><InfoFilled /></el-icon>
          版本: {{ sdkVersion || 'N/A' }}
        </el-tag>
      </div>
    </div>
    <div class="toolbar-right">
      <!-- 工作空间和分支 -->
      <div class="config-selects">
        <el-form-item label="项目仓库:" class="toolbar-form-item" style="font-size: 14px; font-weight: 300; color: #666;">
          <el-select
            :model-value="form.gitlab"
            @update:model-value="updateGitlab"
            class="input-field"
            placeholder="请选择或输入仓库地址"
            filterable
            allow-create
            clearable
            :disabled="!hasEditPermission"
            style="border: none; padding: 0px"
            @visible-change="handleGitlabClick"
          >
            <el-option
              v-for="item in spaceOptions"
              :key="item"
              :label="item.split('/').pop()"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="工作空间:" class="toolbar-form-item" style="font-size: 14px; font-weight: 300; color: #666;">
          <el-select
            :model-value="form.project_branch"
            @update:model-value="updateBranch"
            class="input-field"
            placeholder="请选择或输入分支"
            filterable
            allow-create
            clearable
            :disabled="!hasEditPermission"
            style="border: none; padding: 0px;"
          >
            <el-option
              v-for="branch in branchOptions"
              :key="branch"
              :label="branch"
              :value="branch"
            />
          </el-select>
        </el-form-item>
      </div>
      <!-- 功能按钮 -->
      <div class="button-group">
        <el-button type="primary" :disabled="!hasEditPermission" @click="handDownload">
          Download
        </el-button>
        <el-button type="primary" :disabled="!hasEditPermission" @click="handleSave">
          Commit
        </el-button>
        <el-button type="success" :disabled="!hasEditPermission" @click="handlePublish">
          Push
        </el-button>
        <el-button type="danger" :disabled="!hasEditPermission" @click="mergetest">
          Merge
        </el-button>
      </div>
    </div>
    
    <!-- 合并分支弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      title="选择合并分支"
      width="400px"
      :show-close="true"
      center
      class="merge-dialog"
    >
      <div class="dialog-content">
        <el-form label-position="top">
          <el-form-item label="目标分支">
            <el-select v-model="mergeBranch" :placeholder="mergeBranch" style="width: 100%">
              <el-option v-for="item in ['dev']" :key="item" :label="item" :value="item">
                {{ item }}
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmAction">确定合并</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { InfoFilled } from '@element-plus/icons-vue';

// Props
const props = defineProps({
  sdkVersion: {
    type: String,
    default: ''
  },
  form: {
    type: Object,
    required: true
  },
  spaceOptions: {
    type: Array,
    default: () => []
  },
  branchOptions: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  hasEditPermission: {
    type: Boolean,
    default: true
  }
});

// Emits
const emit = defineEmits([
  'update:form',
  'gitlab-click',
  'save',
  'publish',
  'merge-test',
  'confirm-action'
]);



// 响应式数据
const dialogVisible = ref(false);
const mergeBranch = ref('dev');




const handDownload = () => {
  
}

// 直接使用props中的form，通过事件更新
const updateGitlab = (value) => {
  emit('update:form', { ...props.form, gitlab: value });
};

const updateBranch = (value) => {
  emit('update:form', { ...props.form, project_branch: value });
};

// 事件处理函数
const handleGitlabClick = (visible) => {
  emit('gitlab-click', visible);
};


const mergetest = () => {
  dialogVisible.value = true;
  emit('merge-test');
};


// 功能按钮
// commit 操作
const handleSave = async () => {
  ElMessageBox.prompt('请输入 commit 信息', '保存配置', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    inputErrorMessage: '请输入 commit 信息',
    inputValidator: (value) => value != null && value.trim() !== ''
  }).then(({ value }) => {
    loading.value = true;
    http.post('/code_management/config_commit', {
      params: { commit_message: value, workspace_path: workspace.value, branch_status: branch_status.value },
      timeout: 60000
    }).then(response => {
      loading.value = false;
      if (response.data.commit_status === 1) {
        messageManager.success('配置已保存:commit成功');
      } else {
        messageManager.error('配置保存失败:commit失败');
      }
    }).catch(() => {
      loading.value = false;
      messageManager.error('保存失败');
    });
  }).catch(() => {
    messageManager.info('取消保存');
  });
};

// push 操作
const handlePublish = () => {
  ElMessageBox.confirm(
    '确定要发布当前配置吗？发布后将生效并覆盖现有配置。',
    '发布确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    loading.value = true;
    http.post('/code_management/config_push', {
      params: { workspace_path: workspace.value, branch_status: branch_status.value },
      timeout: 60000
    }).then(response => {
      loading.value = false;
      if (response.data.push_status === 1) {
        messageManager.success('配置已发布');
      } else {
        messageManager.error('配置发布失败');
      }
    }).catch(() => {
      loading.value = false;
      messageManager.info('已取消发布');
    });
  }).catch(() => {
    messageManager.info('已取消发布');
  });
};

// merge 确认操作
const confirmAction = (mergeBranch) => {
  ElMessageBox.confirm(
    '确定要merge当前分支到目标分支吗？',
    '发布确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    loading.value = true;
    http.post('/code_management/merge_project', {
      params: { workspace_path: workspace.value, merge_branch: mergeBranch },
      timeout: 60000
    }).then(() => {
      messageManager.success('已发送merge信息,请等待管理员审批');
      loading.value = false;
    }).catch(() => {
      messageManager.error('merge失败');
      loading.value = false;
    });
  }).catch(() => {
    messageManager.info('已取消merge');
  });
};


</script>

<style scoped>
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 35px 30px;
  height: 50px;
  background: #fff;
  color: white;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  position: relative;
  border-radius: 8px;
  /* margin: 0px 20px; */
}


.toolbar-left {
  display: flex;
  align-items: center;
  gap: 20px;
}
.version-info {
  margin-right: 20px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.config-selects {
  display: flex;
  align-items: center;
  gap: 16px;
}

.toolbar-form-item {
  margin-bottom: 0;
}

.input-field {
  min-width: 150px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.merge-dialog {
  border-radius: 8px;
}

.dialog-content {
  padding: 20px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>
