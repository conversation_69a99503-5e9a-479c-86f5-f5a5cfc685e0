<template>
  <div class="test-submission-container">
    <!-- 页面标题和操作区 -->
    <div class="page-header">
      <div class="header-right">
        <!-- But<PERSON> moved -->
      </div>
    </div>
    
    <!-- 表格筛选区 -->
    <div class="filter-container">
      <el-tabs v-model="activeTab" class="version-tabs" @tab-click="handleTabChange">
        <el-tab-pane label="全部版本" name="all"></el-tab-pane>
      </el-tabs>
    </div>

    <!-- 新增按钮区域 -->
    <div class="add-button-container">
       <el-button type="primary" @click="openSubmissionDialog">+ 新增软件版本提测</el-button>
    </div>
    
    <!-- 版本列表表格 -->
    <div class="table-container">
      <el-table 
        :data="submissionList" 
        style="width: 100%" 
        border 
        v-loading="loading"
        row-key="id"
        :header-cell-style="{background:'#f5f7fa',color:'#606266'}"
      >
        <el-table-column prop="project" label="所属项目" min-width="150" />
        <el-table-column prop="title" label="产品版本类型" min-width="180" />
        <el-table-column prop="version" label="产品版本类型详细信息" min-width="200">
          <template #default="scope">
            <div style="white-space: pre-line;">{{ scope.row.version }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="submitTime" label="提交日期" min-width="120" />  
        <el-table-column prop="engineeringProject" label="工程组" min-width="120" />
        <el-table-column prop="engineeringPath" label="工程路径" min-width="150" />
        <el-table-column prop="status" label="版本状态" min-width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="originalBranch" label="源分支" min-width="120" />
        <el-table-column prop="targetBranch" label="目标分支" min-width="120" />
        <el-table-column prop="publisher" label="发布人" min-width="100" />
        <el-table-column prop="content" label="软件版本变更内容" min-width="200">
          <template #default="scope">
            <div style="white-space: pre-line;">{{ scope.row.content }}</div>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="180">
          <template #default="scope">
            <div class="button-group">
              <el-button 
                size="small" 
                type="primary" 
                @click="auditSubmission(scope.row)"
                :disabled="scope.row.status !== '待审核'"
                class="action-button"
              >审核</el-button>
              <el-button 
                size="small" 
                type="primary" 
                @click="deleteSubmission(scope.row)"
                :disabled="scope.row.status === '已完成'"
                class="action-button"
              >删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-size="pageSize"
          :current-page="currentPage"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    
    <!-- 新增/编辑提测弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '新增软件版本提测' : '编辑软件版本提测'"
      width="75%"
      top="15vh"
      :fullscreen="false"
      center
    >
      <el-form :model="submissionForm" label-width="220px" :rules="rules" ref="submissionFormRef">
        <el-form-item label="所属项目" prop="project">
          <Projects ref="projectsRef" v-model="submissionForm.project" :includePrefix="false" @change="onProjectChange" />
        </el-form-item>
        <el-form-item label="产品软件类型" prop="softwareType">
          <el-select v-model="submissionForm.softwareType" placeholder="请选择产品软件类型" style="width: 100%" @change="handleSoftwareTypeChange">
            <el-option label="产品显示屏MCU版本" value="display" />
            <el-option label="产品电机MCU版本" value="motor" />
            <el-option label="产品图形化MCU版本" value="graphic" />
          </el-select>
        </el-form-item>
        
        <!-- 产品显示屏MCU版本相关字段 -->
        <template v-if="submissionForm.softwareType === 'display'">
          <el-form-item label="客户软件版本">
            <el-input v-model="submissionForm.software" placeholder="请输入软件版本号" @input="validateSoftwareFields" />
          </el-form-item>
          <el-form-item label="海微内部软件版本">
            <el-input v-model="submissionForm.hw_software" placeholder="请输入HW内部的软件版本" @input="validateSoftwareFields" />
          </el-form-item>
          <el-form-item label="海微BOOT版本">
            <el-input v-model="submissionForm.hw_boot" placeholder="请输入BOOT版本号" @input="validateSoftwareFields" />
          </el-form-item>
        </template>
        
        <!-- 产品电机MCU版本相关字段 -->
        <template v-else-if="submissionForm.softwareType === 'motor'">
          <el-form-item label="客户软件版本">
            <el-input v-model="submissionForm.motor_software" placeholder="请输入软件版本号" @input="validateSoftwareFields" />
          </el-form-item>
          <el-form-item label="海微电机软件版本">
            <el-input v-model="submissionForm.hw_motor_software" placeholder="请输入HW电机软件版本" @input="validateSoftwareFields" />
          </el-form-item>
          <el-form-item label="海微电机BOOT版本">
            <el-input v-model="submissionForm.hw_motor_boot" placeholder="请输入HW电机BOOT版本号" @input="validateSoftwareFields" />
          </el-form-item>
        </template>
        
        <!-- 产品图形显示MCU版本相关字段 -->
        <template v-else-if="submissionForm.softwareType === 'graphic'">
          <el-form-item label="客户软件版本">
            <el-input v-model="submissionForm.osd_software" placeholder="请输入软件版本号" @input="validateSoftwareFields" />
          </el-form-item>
          <el-form-item label="海微内部软件版本">
            <el-input v-model="submissionForm.hw_osd_software" placeholder="请输入HW内部软件版本" @input="validateSoftwareFields" />
          </el-form-item>
          <el-form-item label="海微BOOT版本">
            <el-input v-model="submissionForm.hw_osd_boot" placeholder="请输入BOOT软件版本号" @input="validateSoftwareFields" />
          </el-form-item>
        </template>
        
        <el-form-item label="软件版本变更内容" prop="change_description">
          <el-input 
            v-model="submissionForm.change_description" 
            type="textarea" 
            rows="6"
            style="width: 100%; min-height: 150px;"
            placeholder="请输入软件版本变更内容描述" 
          />
        </el-form-item>
        <el-form-item label="工程组" prop="engineeringProject">
          <el-select 
            v-model="submissionForm.engineeringProject" 
            placeholder="请选择工程组" 
            style="width: 100%" 
            @change="handleEngineeringProjectChange"
          >
            <el-option label="mcu-team" value="mcu-team" />
            <el-option label="os-team" value="os-team" />
            <el-option label="app-team" value="app-team" />
          </el-select>
        </el-form-item>
        <el-form-item label="工程路径" prop="engineeringPath">
          <el-select 
            v-model="submissionForm.engineeringPath" 
            placeholder="请选择工程路径" 
            style="width: 100%"
            filterable
            @change="handleProjectChange"
            remote
            :remote-method="handleProjectSearch"
          >
            <el-option 
              v-for="item in filteredProjects" 
              :key="item.project" 
              :label="item.project" 
              :value="item.project" 
            />
          </el-select>
        </el-form-item>
        <el-form-item label="源分支" prop="originalBranch">
          <el-select 
            v-model="submissionForm.originalBranch" 
            placeholder="请选择源分支" 
            style="width: 100%"
            :disabled="!submissionForm.engineeringPath"
            @change="handleOriginalBranchChange"
          >
            <el-option 
              v-for="branch in availableBranches" 
              :key="branch" 
              :label="branch" 
              :value="branch" 
            />
          </el-select>
        </el-form-item>
        <el-form-item label="目标分支" prop="targetBranch">
          <el-select 
            v-model="submissionForm.targetBranch" 
            placeholder="请选择目标分支" 
            style="width: 100%"
            :disabled="!submissionForm.engineeringPath"
            @change="validateBranches"
          >
            <el-option 
              v-for="branch in availableBranches" 
              :key="branch" 
              :label="branch" 
              :value="branch" 
            />
          </el-select>
        </el-form-item>
        
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, onUnmounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import Projects from '@/components/projects.vue';
import http from '@/utils/http/http.js';

// 状态数据
const loading = ref(false);
const dialogVisible = ref(false);
const dialogType = ref('add'); // 'add' 或 'edit'
const submissionList = ref([]);
const total = ref(0);
const pageSize = ref(10);
const currentPage = ref(1);
const submissionFormRef = ref(null);
const activeTab = ref('all');

// 表单数据
const submissionForm = reactive({
  id: '',
  project: '',
  softwareType: '', // 设置默认值
  // 显示屏MCU版本字段
  software: '',
  hw_software: '',
  hw_boot: '',
  // 电机MCU版本字段
  motor_software: '',
  hw_motor_software: '',
  hw_motor_boot: '',
  // 图形显示MCU版本字段
  osd_software: '',
  hw_osd_software: '',
  hw_osd_boot: '',
  // 其他字段
  change_description: '',
  engineeringProject: '',
  engineeringPath: '',
  originalBranch: '',
  targetBranch: '',
  status: '待审核'
});

// 自定义验证规则，确保每种类型至少填写一个版本字段
const validateVersionFields = (rule, value, callback) => {
  if (submissionForm.softwareType === 'display') {
    // 检查显示屏版本字段
    if (!submissionForm.software && !submissionForm.hw_software && !submissionForm.hw_boot) {
      callback(new Error('请至少填写一个版本字段'));
    } else {
      callback();
    }
  } else if (submissionForm.softwareType === 'motor') {
    // 检查电机版本字段
    if (!submissionForm.motor_software && !submissionForm.hw_motor_software && !submissionForm.hw_motor_boot) {
      callback(new Error('请至少填写一个版本字段'));
    } else {
      callback();
    }
  } else if (submissionForm.softwareType === 'graphic') {
    // 检查图形显示版本字段
    if (!submissionForm.osd_software && !submissionForm.hw_osd_software && !submissionForm.hw_osd_boot) {
      callback(new Error('请至少填写一个版本字段'));
    } else {
      callback();
    }
  } else {
    callback();
  }
};

// 表单验证规则
const rules = {
  project: [{ required: true, message: '请选择所属项目', trigger: 'change' }],
  softwareType: [
    { required: true, message: '请选择产品软件类型', trigger: 'change' },
    { validator: validateVersionFields, trigger: 'blur' }
  ],
  change_description: [{ required: false, message: '请输入软件版本变更内容', trigger: 'blur' }],   // 这个是非必填项
  engineeringProject: [{ required: true, message: '请输入工程组', trigger: 'blur' }],
  engineeringPath: [{ required: true, message: '请输入工程路径', trigger: 'blur' }],
  originalBranch: [{ required: true, message: '请输入源分支', trigger: 'blur' }],
  targetBranch: [{ required: true, message: '请输入目标分支', trigger: 'blur' }]
};


const handleTabChange = () => {
  fetchData(); 
};

// 模拟数据加载
onMounted(() => {
  fetchData();
});

// 获取测试数据
const fetchData = () => {
  loading.value = true;
  
  // 调用实际API获取数据
  http.get('/softtrack/submitsoftware', {
    params: {
      page: currentPage.value,
      page_size: pageSize.value
    }
  })
    .then(response => {
      if (response.data && response.data.results) {
        submissionList.value = response.data.results.map(item => {
          let versionInfo = '';
          
          if (item.software_type === 'display') {
            // 处理显示屏MCU版本字段
            const versions = [];
            if (item.software) versions.push(`软件版本号: ${item.software}`);
            if (item.hw_software) versions.push(`HW内部软件版本: ${item.hw_software}`);
            if (item.hw_boot) versions.push(`BOOT版本号: ${item.hw_boot}`);
            versionInfo = versions.join('\n');
          } else if (item.software_type === 'motor') {
            // 处理电机MCU版本字段
            const versions = [];
            if (item.motor_software) versions.push(`软件版本号: ${item.motor_software}`);
            if (item.hw_motor_software) versions.push(`HW电机软件版本: ${item.hw_motor_software}`);
            if (item.hw_motor_boot) versions.push(`HW电机BOOT版本号: ${item.hw_motor_boot}`);
            versionInfo = versions.join('\n');
          } else if (item.software_type === 'graphic') {
            // 处理图形显示MCU版本字段
            const versions = [];
            if (item.osd_software) versions.push(`软件版本号: ${item.osd_software}`);
            if (item.hw_osd_software) versions.push(`HW内部软件版本: ${item.hw_osd_software}`);
            if (item.hw_osd_boot) versions.push(`BOOT版本号: ${item.hw_osd_boot}`);
            versionInfo = versions.join('\n');
          }
          
          // 如果没有任何版本信息，则显示主版本号
          if (!versionInfo && item.main_version) {
            versionInfo = `主版本号: ${item.main_version}`;
          }
          
          return {
            id: item.id,
            project: item.project,
            title: item.software_type === 'display' ? '产品显示屏MCU版本' :
                   item.software_type === 'motor' ? '产品电机MCU版本' :
                   item.software_type === 'graphic' ? '产品图形化MCU版本' : '',
            version: versionInfo,
            submitTime: item.creation_time,
            engineeringProject: item.engineering_group || '',
            engineeringPath: item.engineering_path || '',
            status: item.status || '待审核',
            originalBranch: item.source_branch || '',
            targetBranch: item.target_branch || '',
            publisher: item.creator || '',
            content: item.change_description || ''
          };
        });
        
        total.value = response.data.count || submissionList.value.length;
      } else {
        submissionList.value = [];
        total.value = 0;
      }
    })
    .catch(error => {
      console.error('获取测试数据失败:', error);
      ElMessage.error('获取数据失败，请稍后重试');
      submissionList.value = [];
      total.value = 0;
    })
    .finally(() => {
      loading.value = false;
    });
};

// 根据状态获取Tag类型
const getStatusType = (status) => {
  switch (status) {
    case '待审核':
      return 'warning';
    case '测试中':
      return 'primary';
    case '已审核':
      return 'success';
    case '已拒绝':
      return 'danger';
    default:
      return 'info';
  }
};

// 打开新增提测弹窗
const openSubmissionDialog = () => {
  dialogType.value = 'add';
  resetForm();
  dialogVisible.value = true;
};


// 删除提测
const deleteSubmission = (row) => {
  ElMessageBox.confirm(
    `确定要删除"${row.title}"提测记录吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 这里应该调用删除API
    http.delete('/softtrack/submitsoftware', {
      params: {
        id: row.id
      }
    })
    .then(response => {
      if (response.data.code === 200) {
        ElMessage.success('删除成功');
        fetchData(); // 刷新数据
      } else {
        ElMessage.error('删除失败');
      }
    })
    .catch(error => {
      console.error('删除失败:', error);
      ElMessage.error('删除失败');
    })
    .finally(() => {
      loading.value = false;
    });
  }).catch(() => {
    // 取消删除
  });
};

// 重置表单
const resetForm = () => {
  if (submissionFormRef.value) {
    submissionFormRef.value.resetFields();
  }
  Object.assign(submissionForm, {
    id: '',
    project: '',
    softwareType: '',
    // 显示屏MCU版本字段
    software: '',
    hw_software: '',
    hw_boot: '',
    // 电机MCU版本字段
    motor_software: '',
    hw_motor_software: '',
    hw_motor_boot: '',
    // 图形显示MCU版本字段
    osd_software: '',
    hw_osd_software: '',
    hw_osd_boot: '',
    // 其他字段
    change_description: '',
    engineeringProject: '',
    engineeringPath: '',
    originalBranch: '',
    targetBranch: '',
    status: '待审核'
  });
};

// 提交表单
const submitForm = () => {
  // 先进行版本字段的特殊验证
  let versionFieldsValid = true;
  
  if (submissionForm.softwareType === 'display') {
    versionFieldsValid = !!(submissionForm.software || submissionForm.hw_software || submissionForm.hw_boot);
  } else if (submissionForm.softwareType === 'motor') {
    versionFieldsValid = !!(submissionForm.motor_software || submissionForm.hw_motor_software || submissionForm.hw_motor_boot);
  } else if (submissionForm.softwareType === 'graphic') {
    versionFieldsValid = !!(submissionForm.osd_software || submissionForm.hw_osd_software || submissionForm.hw_osd_boot);
  }
  
  if (!versionFieldsValid) {
    ElMessage.error('请至少填写一个版本字段');
    return;
  }

  // 继续常规表单验证
  submissionFormRef.value.validate((valid) => {
    if (valid) {
      // 显示加载状态
      loading.value = true;
      
      // 确定main_version字段（使用第一个非空的版本字段）
      let mainVersion = '';
      if (submissionForm.softwareType === 'display') {
        mainVersion = submissionForm.software || submissionForm.hw_software || submissionForm.hw_boot;
      } else if (submissionForm.softwareType === 'motor') {
        mainVersion = submissionForm.motor_software || submissionForm.hw_motor_software || submissionForm.hw_motor_boot;
      } else if (submissionForm.softwareType === 'graphic') {
        mainVersion = submissionForm.osd_software || submissionForm.hw_osd_software || submissionForm.hw_osd_boot;
      }
      
      // 打包表单数据
      const formData = {
        project: submissionForm.project,
        software_type: submissionForm.softwareType,
        main_version: mainVersion,
        change_description: submissionForm.change_description,
        engineering_group: submissionForm.engineeringProject,
        engineering_path: submissionForm.engineeringPath,
        source_branch: submissionForm.originalBranch,
        target_branch: submissionForm.targetBranch,
        status: '待审核'
      };
      
      // 根据软件类型添加相应字段
      if (submissionForm.softwareType === 'display') {
        formData.software = submissionForm.software;
        formData.hw_software = submissionForm.hw_software;
        formData.hw_boot = submissionForm.hw_boot;
      } else if (submissionForm.softwareType === 'motor') {
        formData.motor_software = submissionForm.motor_software;
        formData.hw_motor_software = submissionForm.hw_motor_software;
        formData.hw_motor_boot = submissionForm.hw_motor_boot;
      } else if (submissionForm.softwareType === 'graphic') {
        formData.osd_software = submissionForm.osd_software;
        formData.hw_osd_software = submissionForm.hw_osd_software;
        formData.hw_osd_boot = submissionForm.hw_osd_boot;
      }
      
      // 发送POST请求到softtrack接口
      http.post('/softtrack/submitsoftware', formData)
        .then(response => {
          if (response.data && response.data.id) {
            // 请求成功
            ElMessage.success('新增提测成功');
            
            // 关闭对话框
            dialogVisible.value = false;
            
            // 刷新列表数据
            fetchData();
          } else {
            // 请求返回但状态码不是200
            ElMessage.error(`提交失败: ${response.data.message || '未知错误'}`);
          }
        })
        .catch(error => {
          // 请求失败
          console.error('提交提测信息失败:', error);
          ElMessage.error(`提交失败: ${error.message || '网络错误'}`);
        })
        .finally(() => {
          // 无论成功或失败，都关闭加载状态
          loading.value = false;
        });
    }
  });
};

// 分页相关方法
const handleSizeChange = (size) => {
  pageSize.value = size;
  fetchData();
};

const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchData();
};

// 项目选择变更处理
const projectsRef = ref(null);
const onProjectChange = (projectInfo) => {
  if (projectInfo) {
    // 如果需要，可以在这里处理项目变更逻辑
    console.log('项目已变更:', projectInfo);
  }
};

// 处理软件类型变更
const handleSoftwareTypeChange = (value) => {
  console.log('软件类型变更为:', value);
  // 清空之前的版本号字段
  
  // 手动触发验证
  if (submissionFormRef.value) {
    submissionFormRef.value.validateField('softwareType');
  }
};

// 验证软件版本字段
const validateSoftwareFields = () => {
  if (submissionFormRef.value) {
    submissionFormRef.value.validateField('softwareType');
  }
};

// 添加一个新的响应式变量来存储API返回的项目和分支数据
const projectsAndBranches = ref([]);
const filteredProjects = ref([]);

// 处理工程组变更
const handleEngineeringProjectChange = (value) => {
  console.log('工程组变更为:', value);
  
  // 调用softtrackgitgroup接口
  if (value) {
    http.get('/softtrack/gitgroup', { 
      params: { 
        engineering_group: value 
      },
      timeout: 90000  // 设置超时时间为90秒
    })
    .then(response => {
      console.log('获取到工程组信息:', response.data);
      
      // 保存API返回的项目和分支数据
      if (response.data && response.data.projects) {
        projectsAndBranches.value = response.data.projects;
        // 初始化过滤后的项目列表
        filteredProjects.value = response.data.projects;
        
        // 清空当前选择的项目和分支
        submissionForm.engineeringPath = '';
        submissionForm.originalBranch = '';
        submissionForm.targetBranch = '';
      }
    })
    .catch(error => {
      console.error('获取工程组信息失败:', error);
      ElMessage.error('获取工程组相关信息失败');
    });
  }
};

// 根据选择的项目过滤分支
const availableBranches = computed(() => {
  if (!submissionForm.engineeringPath) return [];
  
  const selectedProject = projectsAndBranches.value.find(
    p => p.project === submissionForm.engineeringPath
  );
  
  return selectedProject ? selectedProject.branch : [];
});

// 处理工程路径搜索
const handleProjectSearch = (query) => {
  if (query) {
    filteredProjects.value = projectsAndBranches.value.filter(
      p => p.project.toLowerCase().includes(query.toLowerCase())
    );
  } else {
    filteredProjects.value = projectsAndBranches.value;
  }
};

// 处理工程路径变更
const handleProjectChange = () => {
  // 当选择新项目时，清空已选分支
  submissionForm.originalBranch = '';
  submissionForm.targetBranch = '';
};

// 验证源分支和目标分支
const validateBranches = () => {
  if (submissionForm.originalBranch && submissionForm.targetBranch && 
      submissionForm.originalBranch === submissionForm.targetBranch) {
    ElMessageBox.alert(
      '源分支和目标分支不能相同，请重新选择！',
      '警告',
      {
        confirmButtonText: '确定',
        type: 'warning',
      }
    );
    // 清空目标分支选择
    submissionForm.targetBranch = '';
  }
};


const handleOriginalBranchChange = () => {
  if (submissionForm.targetBranch && submissionForm.originalBranch === submissionForm.targetBranch) {
    ElMessageBox.alert(
      '源分支和目标分支不能相同，请重新选择！',
      '警告',
      {
        confirmButtonText: '确定',
        type: 'warning',
      }
    );
    // 当源分支变更后与目标分支相同，也清空目标分支
    submissionForm.targetBranch = '';
  }
};

// 添加审核提交函数
const auditSubmission = (row) => {
  ElMessageBox.confirm(
    `确定要审核"${row.title}"提测记录吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    }
  ).then(() => {
    loading.value = true;
    
    const auditData = {
      engineering_path: row.engineeringPath,
      source_branch: row.originalBranch,
      target_branch: row.targetBranch,
      engineering_group: row.engineeringProject,
      id: row.id
    };
    
    // 发送POST请求到审核接口
    http.post('/softtrack/mergerequest', auditData)
      .then(response => {
        if (response.data && (response.data.mr_iid || response.data.success)) {
          ElMessage.success('审核操作已提交');
          
          // 如果返回了mr_iid，开始轮询合并请求状态
          if (response.data.mr_iid) {
            const projectPath = response.data.project || row.engineeringPath;
            const mrIid = response.data.mr_iid;
            
            // 启动轮询
            startPollingMergeRequestStatus(projectPath, mrIid, row);
          } else {
            // 如果没有返回mr_iid，仅刷新数据
            fetchData();
          }
        } else {
          ElMessage.error(`审核失败: ${response.data?.message || '未知错误'}`);
        }
      })
      .catch(error => {
        console.error('审核操作失败:', error);
        ElMessage.error(`审核失败: ${error.message || '网络错误'}`);
      })
      .finally(() => {
        loading.value = false;
      });
  }).catch(() => {
    // 取消审核操作
  });
};

// 添加轮询函数
const pollingTimers = ref({}); // 存储定时器ID，以便可以清除

const startPollingMergeRequestStatus = (projectPath, mrIid, row) => {
  // 创建唯一的定时器ID
  const timerId = `${projectPath}-${mrIid}`;
  
  // 清除可能存在的旧定时器
  if (pollingTimers.value[timerId]) {
    clearInterval(pollingTimers.value[timerId]);
  }
  
  // 显示初始状态通知
  ElMessage({
    message: `正在检查合并请求状态，请稍候...`,
    type: 'info',
    duration: 3000
  });
  
  // 创建新的定时器，每5秒轮询一次
  pollingTimers.value[timerId] = setInterval(() => {
    http.get('/softtrack/mr-status/', {
      params: {
        engineering_group: row.engineeringProject,
        project_path: projectPath,
        mr_iid: mrIid
      }
    })
    .then(response => {
      if (response.data) {
        const { merge_status, state,code } = response.data;
        
        // 根据状态更新UI或显示通知
        if (merge_status === 'can_be_merged' && state === 'opened') {
          ElMessage({
            message: '合并请求已准备好,将进行自动合并',
            type: 'success',
            duration: 5000
          });
          
          http.post('/softtrack/mr-status/', {             // 发送POST请求来执行合并
            project_path: projectPath,
            mr_iid: mrIid,
            id: row.id
          })
          .then(mergeResponse => {
            console.log('自动合并已触发:', mergeResponse.data);
            if (mergeResponse.data && mergeResponse.data.code == 200) {
              ElMessage({
                message: mergeResponse.data.message,
                type: 'success',
                duration: 5000
              });
            } else {
              ElMessage({
                message: mergeResponse.data.message,
                type: 'error',
                duration: 5000
              });
            }
          })
          .catch(mergeError => {
            console.error('执行合并失败:', mergeError);
            ElMessage.error(`执行合并失败: ${mergeError.message || '网络错误'}`);
          });
          
          // 停止轮询，因为已经触发了合并操作
          clearInterval(pollingTimers.value[timerId]);
          delete pollingTimers.value[timerId];
        } 
        else if (state === 'closed') {
          ElMessage({
            message: '当前合并请求失败,请检查是否提交远端dev分支,或者是否存在冲突',
            type: 'warning',
            duration: 5000
          });
          // 停止轮询
          clearInterval(pollingTimers.value[timerId]);
          delete pollingTimers.value[timerId];
          // 刷新数据
          fetchData();
        }   
      }
    })
    .catch(error => {
      console.error('轮询合并请求状态失败:', error);
      clearInterval(pollingTimers.value[timerId]);
      delete pollingTimers.value[timerId];
      ElMessage.error( `当前合并失败,异常是 ${error.response.data.message}`);
      
    });
  }, 5000); // 每5秒轮询一次
};

// 在组件卸载时清除所有定时器
onUnmounted(() => {
  Object.values(pollingTimers.value).forEach(timerId => {
    clearInterval(timerId);
  });
  pollingTimers.value = {};
});
</script>

<style scoped>
.test-submission-container {
  padding: 16px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 80px);
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.page-title {
  font-size: 22px;
  color: #303133;
  margin: 0;
}

.filter-container {
  background-color: #fff;
  padding: 10px 20px 0;
  margin-bottom: 16px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.add-button-container {
  margin-bottom: 16px; /* Add space below the button */
  padding: 0 20px; /* Align with table padding */
  background-color: #fff; /* Match table background */
  padding-top: 16px; /* Add space above the button */
  padding-bottom: 16px; /* Add space below the button */
  border-radius: 4px 4px 0 0; /* Round top corners if desired */
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  margin-top: -16px; /* Pull up slightly to connect with tabs visually */
  position: relative; /* Ensure z-index works if needed */
  z-index: 1; /* Ensure it's above the table container shadow if overlapping */
}

.table-container {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  width: 100%;
  overflow-x: auto;
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-bottom: 0;
  padding-bottom: 40px;
}

.el-table {
  width: 100% !important;
  min-width: 100%;
  flex-grow: 1;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.version-tabs :deep(.el-tabs__item) {
  padding: 0 20px;
  height: 40px;
  line-height: 40px;
}

.version-tabs :deep(.el-tabs__nav) {
  border: none;
}

.version-tabs :deep(.el-tabs__active-bar) {
  height: 3px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

/* 调整表格样式 */
:deep(.el-table th) {
  font-weight: 600;
  color: #606266;
  background-color: #f5f7fa !important;
}

:deep(.el-table .cell) {
  padding-left: 12px;
  padding-right: 12px;
}

:deep(.el-button--text) {
  padding: 4px 8px;
}

/* Ensure the single tab looks correct */
.filter-container .el-tabs__nav-wrap::after {
  /* Hide the bottom border of the tabs container if desired */
  background-color: transparent;
}

.el-form :deep(.el-form-item__label) {
  font-weight: 500;
}

.el-dialog :deep(.el-dialog__body) {
  padding: 20px 30px;
  max-height: 80vh;
  overflow-y: auto;
}

.el-dialog :deep(.el-dialog__header) {
  padding: 15px 20px;
  margin-right: 0;
  border-bottom: 1px solid #ebeef5;
}

.el-dialog :deep(.el-dialog__title) {
  font-weight: 600;
  font-size: 18px;
}

.el-dialog :deep(.el-dialog__footer) {
  border-top: 1px solid #ebeef5;
  padding: 15px 20px;
}

.el-dialog :deep(.el-input),
.el-dialog :deep(.el-select),
.el-dialog :deep(.el-date-picker) {
  width: 70%;
}

.el-dialog :deep(.el-textarea) {
  width: 85%;
}

/* 操作按钮样式 */
.button-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-button {
  width: 100%;
  margin-right: 0;
}

/* 在大屏幕上使用水平布局 */
@media screen and (min-width: 768px) {
  .button-group {
    flex-direction: row;
    gap: 8px;
  }
  
  .action-button {
    width: 80px;
  }
}
</style> 