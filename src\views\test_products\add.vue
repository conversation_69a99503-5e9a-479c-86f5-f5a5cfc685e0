<template>
    <div class="add-container">
        <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">

            <el-form-item label="所属项目" prop="project_number">
                <Projects v-model="form.project_number" :includePrefix="false" :includeAll="false" ref="projectRef" />
            </el-form-item>

            <el-form-item label="名称" prop="name">
                <el-input v-model="form.name"></el-input>
            </el-form-item>

            <el-form-item label="料号">
                <el-input v-model="form.m_number"></el-input>
            </el-form-item>

            <el-form-item label="硬件版本" prop="hardware_versions">
                <ProductVersion ref="hardVersionsRef" v-model="form.hardware_versions" :project-number="form.project_number"
                    :include="['HARDWARE']" />
            </el-form-item>

            <el-form-item label="软件版本">
                <ProductVersion ref="softVersionsRef" v-model="form.software_versions" :project-number="form.project_number" :exclude="['HARDWARE']" />
            </el-form-item>

            <el-form-item label="组件">
                <el-select v-model="form.components" multiple placeholder="请选择组件" filterable clearable>
                    <el-option v-for="item in testProducts" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
            </el-form-item>

            <div class="submit-button-container">
                <el-button type="default" @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onConfirm">提交</el-button>
            </div>

        </el-form>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import http from '@/utils/http/http.js';
import Projects from '@/components/projects.vue';
import ProductVersion from '@/components/product-version.vue';

const formRef = ref(null);
const projectRef = ref(null);
const softVersionsRef = ref(null);
const hardVersionsRef = ref(null);
const testProducts = ref([]);

const form = ref({
    project_number: '',
    name: '',
    m_number: '',
    hardware_versions: [],
    software_versions: [],
    components: [],
});

const rules = ref({
    project_number: [
        { required: true, message: '请选择所属项目', trigger: 'change' },
    ],
    name: [
        { required: true, message: '请输入名称', trigger: 'blur' },
    ],
    hardware_versions: [
        { required: true, message: '请输入硬件版本', trigger: 'blur' },
    ],
});

const emit = defineEmits(['confirm', 'cancel'])

const onConfirm = () => {
    formRef.value.validate(async (valid) => {
        if (valid) {
            let data = {
                ...form.value,
            };

            let project_info = projectRef.value.getProjectInfo(form.value.project_number);
            data.project_id = project_info?.id;
            data.project_name = project_info?.name;

            let hardware_versions = hardVersionsRef.value.getSelected();
            data.hardware_versions = JSON.stringify(hardware_versions);

            let software_versions = softVersionsRef.value.getSelected();
            data.software_versions = JSON.stringify(software_versions);

            http.post('/test_products', data).then(res => {
                emit('confirm');
            }).catch(err => {
                ElMessageBox.alert(err.response.data.msg, '提交失败', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                })
            });
        };
    });
};

const onCancel = () => {
    emit('cancel');
};

onMounted(() => {

    http.get('/test_products', { params: { page: 1, pageSize: 100000 } }).then(res => {
        testProducts.value = res.data.data.results;
    });

});

</script>


<style lang="scss" scoped>
.add-container {
    padding: 0 20px;
}

.submit-button-container {
    display: flex;
    justify-content: flex-end;
}
</style>