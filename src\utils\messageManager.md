# ElMessage 管理器使用说明

## 概述

`messageManager` 是一个全局的 ElMessage 管理器，确保界面上只显示一条消息。如果存在历史消息，会先清除历史消息再显示新消息。

## 特性

- **单例模式**: 全局只有一个消息管理器实例
- **自动清理**: 新消息会自动清除历史消息
- **类型支持**: 支持 success、error、warning、info 四种消息类型
- **自动回调**: 消息关闭时自动清理内部状态

## 使用方法

### 1. 导入

```javascript
import messageManager from '@/utils/messageManager';
```

### 2. 基本用法

```javascript
// 显示成功消息
messageManager.success('操作成功！');

// 显示错误消息
messageManager.error('操作失败！');

// 显示警告消息
messageManager.warning('请注意！');

// 显示信息消息
messageManager.info('提示信息');
```

### 3. 自定义显示时长

```javascript
// 显示5秒的成功消息
messageManager.success('操作成功！', 5000);

// 显示10秒的错误消息
messageManager.error('操作失败！', 10000);
```

### 4. 手动清除消息

```javascript
// 清除当前显示的消息
messageManager.clear();
```

## API 参考

### success(message, duration)
显示成功消息
- `message` (string): 消息内容
- `duration` (number): 显示时长，默认3000ms
- 返回: 消息实例

### error(message, duration)
显示错误消息
- `message` (string): 消息内容
- `duration` (number): 显示时长，默认3000ms
- 返回: 消息实例

### warning(message, duration)
显示警告消息
- `message` (string): 消息内容
- `duration` (number): 显示时长，默认3000ms
- 返回: 消息实例

### info(message, duration)
显示信息消息
- `message` (string): 消息内容
- `duration` (number): 显示时长，默认3000ms
- 返回: 消息实例

### clear()
清除当前显示的消息

## 迁移指南

### 从 ElMessage 迁移

**之前的代码:**
```javascript
import { ElMessage } from 'element-plus';

// 需要手动管理消息实例
let currentMessage = null;
if (currentMessage) {
  currentMessage.close();
}
currentMessage = ElMessage.success('成功！');
```

**迁移后的代码:**
```javascript
import messageManager from '@/utils/messageManager';

// 自动管理，无需手动清理
messageManager.success('成功！');
```

## 注意事项

1. **全局单例**: 所有页面共享同一个消息管理器实例
2. **自动清理**: 无需手动管理消息的生命周期
3. **类型安全**: 确保传入正确的消息类型和参数
4. **性能优化**: 避免频繁调用，建议在用户操作完成后再显示消息

## 已应用的页面

- `src/views/code_management/config/index.vue`
- `src/views/code_management/gpio/index.vue`

## 测试页面

可以访问 `src/views/code_management/test-message.vue` 来测试消息管理器的功能。
