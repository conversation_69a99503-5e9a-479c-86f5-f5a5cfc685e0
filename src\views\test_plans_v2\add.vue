<template>
    <div>
        <el-affix :offset="63">
            <div class="top-tool-container">

                <h2>
                    测试计划
                    <el-tag v-if="planStatus == 'DEBUGGING'" type="info" round>调试中</el-tag>
                    <el-tag v-else-if="planStatus == 'REVIEWING'" type="warning" round>评审中</el-tag>
                    <el-tag v-else-if="planStatus == 'APPROVED'" type="success" round>评审通过</el-tag>
                    <el-tag v-else-if="planStatus == 'REJECTED'" type="danger" round>评审不通过</el-tag>
                    <el-tag v-else-if="planStatus == 'RUNNING'" type="success" round>执行中</el-tag>
                    <el-tag v-else-if="planStatus == 'COMPLETED'" type="primary" round>已完成</el-tag>
                </h2>

                <el-button @click="onBack" style="margin-left: auto;margin-right: 10px;">返回</el-button>
                <div class="submit-button-container">
                    <el-tooltip class="box-item" effect="dark" content="保存测试计划并推送到产品开发平台进行计划评审" placement="top">
                        <el-button @click="onPublish" :loading="publishLoading"
                            :disabled="form.plan_type != '0'">发起计划评审</el-button>
                    </el-tooltip>
                    <el-tooltip class="box-item" effect="dark" content="向产品开发平台推送计划关联的版本信息" placement="top">
                        <el-button @click="handlePushRelatedVersions" :loading="pushRelatedVersionsLoading"
                            :disabled="planStatus != 'COMPLETED'">推送关联版本</el-button>
                    </el-tooltip>
                    <el-tooltip class="box-item" effect="dark" content="推送测试用例执行结果到产品开发平台" placement="top">
                        <el-button @click="handleSyncTestPlanResult"
                            :disabled="form.plan_type != '0'">推送测试结果</el-button>
                    </el-tooltip>
                    <el-button type="primary" @click="onSave" :loading="saveLoading">保存</el-button>
                </div>
            </div>
        </el-affix>

        <el-divider style="margin: 0;"></el-divider>

        <el-collapse v-model="activeNames" style="padding: 10px;">
            <el-collapse-item title="基本信息" name="1">
                <el-form :model="form" label-width="auto" :rules="rules" status-icon ref="formRef">
                    <el-row :gutter="15" style="max-width: 1400px;">
                        <el-col :span="10">
                            <el-form-item label="计划名称" prop="name">
                                <el-input v-model="form.name" placeholder="请输入计划名称"></el-input>
                            </el-form-item>
                            <el-form-item label="样品信息">
                                <el-input type="textarea" :rows="3" v-model="form.sample_information"
                                    placeholder="请输入样品信息" maxlength="120" show-word-limit></el-input>
                            </el-form-item>
                            <el-form-item label="计划描述">
                                <el-input v-model="form.desc" type="textarea" :rows="3"
                                    placeholder="请输入计划描述"></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="14">
                            <el-form-item label="所属项目" prop="project_number">
                                <Projects v-model="form.project_number" :includePrefix="false" :includeAll="false"
                                    :disabled="route.query.type === 'edit'" @change="onProjectChange"
                                    ref="projectRef" />
                            </el-form-item>
                            <el-row :gutter="10">
                                <el-col :span="12">
                                    <el-form-item label="测试版本" prop="m_version">
                                        <el-tree-select v-model="form.m_version" lazy clearable :load="loadVersions"
                                            :data="projectVersionData" v-if="projectVersionV"
                                            :props="{ label: 'label', value: 'label', children: 'children', isLeaf: 'isLeaf' }" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="次级版本">
                                        <el-tree-select ref="subVersionsRef" v-model="form.sub_versions" lazy
                                            :load="loadVersions" multiple clearable :data="projectVersionData"
                                            :cache-data="initProjectVersionData" v-if="projectVersionV"
                                            :props="{ label: 'label', value: 'value', children: 'children', isLeaf: 'isLeaf' }">
                                        </el-tree-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-form-item label="关联版本">
                                <el-tree-select ref="productVersionRef" v-model="form.product_version" lazy
                                    :load="loadVersions" multiple clearable :data="projectVersionData"
                                    :cache-data="initProjectVersionData" v-if="projectVersionV"
                                    :props="{ label: 'label', value: 'value', children: 'children', isLeaf: 'isLeaf' }">
                                </el-tree-select>
                            </el-form-item>
                            <el-form-item label="测试产品">
                                <el-select v-model="form.test_product_id" placeholder="请选择测试产品" filterable clearable>
                                    <el-option v-for="item in testProducts" :key="item.id" :label="item.name"
                                        :value="item.id"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-row :gutter="10">
                                <el-col :span="12">
                                    <el-form-item label="测试类型" prop="test_type">
                                        <el-select v-model="form.test_type" placeholder="请选择测试类型"
                                            :disabled="route.query.type === 'edit'">
                                            <el-option v-for="item in test_types" :label="item.label"
                                                :value="item.remark"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="计划用途" prop="plan_use">
                                        <el-select v-model="form.plan_use" placeholder="请选择计划用途">
                                            <el-option label="全功能测试" value="FULL_FUNCTIONALITY_TEST"></el-option>
                                            <el-option label="版本回归测试" value="VERSION_REGRESSION_TEST"></el-option>
                                            <el-option label="专项验证测试" value="SPECIFIC_VALIDATION_TEST"></el-option>
                                            <el-option label="问题验证测试" value="PROBLEM_VALIDATION_TEST"></el-option>
                                            <el-option label="耐久测试" value="DURABILITY_TEST"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="10">
                                <el-col :span="12">
                                    <el-form-item label="是否异常停止" prop="abnormal_stop">
                                        <el-select v-model="form.abnormal_stop">
                                            <el-option label="是" value="1"></el-option>
                                            <el-option label="否" value="0"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="负责人" prop="pic_email">
                                        <Organizaiton v-model="form.pic_email" :cache-data="picInitData" ref="picRef" />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="10">
                                <el-col :span="12">
                                    <el-form-item label="计划开始时间" prop="p_start_time">
                                        <el-date-picker v-model="form.p_start_time" type="date" placeholder="选择计划开始时间"
                                            format="YYYY-MM-DD" value-format="YYYY-MM-DD" style="width: 100%" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="计划结束时间" prop="p_end_time">
                                        <el-date-picker v-model="form.p_end_time" type="date" placeholder="选择计划结束时间"
                                            format="YYYY-MM-DD" value-format="YYYY-MM-DD" style="width: 100%" />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter=10>
                                <el-col :span="12">
                                    <el-form-item label="计划类型" prop="plan_type">
                                        <el-select v-model="form.plan_type" placeholder="请选择计划类型"
                                            :disabled="route.query.type === 'edit'">
                                            <el-option label="对外发布" value="0"></el-option>
                                            <el-option label="内部验证" value="1"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="测试完成通知" prop="finish_notice">
                                        <el-select v-model="form.finish_notice">
                                            <el-option label="是" value="1"></el-option>
                                            <el-option label="否" value="0"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-col>
                    </el-row>
                </el-form>
            </el-collapse-item>

            <el-collapse-item title="测试用例" name="2">
                <div>
                    <div class="tool-bar-container" style="margin-top: 10px;">
                        <el-tooltip class="box-item" effect="dark" content="向测试计划中添加测试用例" placement="top">
                            <el-button icon="Plus" type="primary" plain @click="handleAdd"
                                :disabled="planStatus && !(planStatus == 'DEBUGGING' || planStatus == 'REJECTED')">添加测试用例</el-button>
                        </el-tooltip>
                        <el-tooltip class="box-item" effect="dark" content="对测试计划中保存的所有测试用例进行更新" placement="top">
                            <el-button icon="Refresh" type="primary" plain @click="handleUpdate"
                                :loading="updateLoading"
                                :disabled="planStatus && !(planStatus == 'DEBUGGING' || planStatus == 'REJECTED')">更新全部测试用例</el-button>
                        </el-tooltip>
                        <el-tooltip class="box-item" effect="dark" content="删除选中的测试用例" placement="top">
                            <el-button icon="Minus" type="primary" plain @click="handlePatchDelete"
                                :disabled="planStatus && !(planStatus == 'DEBUGGING' || planStatus == 'REJECTED')">删除</el-button>
                        </el-tooltip>
                        <el-tooltip class="box-item" effect="dark" content="为选中的测试用例指定测试机台" placement="top">
                            <el-button type="primary" plain @click="handlePatchAssignMachine">指定机台</el-button>
                        </el-tooltip>
                        <el-tooltip class="box-item" effect="dark" content="将选中的测试用例设置为上位机不可见" placement="top">
                            <el-button type="primary" plain @click="handlePatchDiable">停用</el-button>
                        </el-tooltip>
                        <el-tooltip class="box-item" effect="dark" content="将选中的测试用例设置为上位机可见" placement="top">
                            <el-button type="primary" plain @click="handlePatchEnable">启用</el-button>
                        </el-tooltip>
                        <el-tooltip class="box-item" effect="dark" content="将选中的测试用例进行评审" placement="top">
                            <el-button type="primary" plain @click="handlePatchReview"
                                :loading="reviewLoading">评审</el-button>
                        </el-tooltip>
                        <el-tooltip class="box-item" effect="dark" content="将选中的测试用例标记为复测" placement="top">
                            <el-button type="primary" plain @click="handlePatchRetest(true)">复测</el-button>
                        </el-tooltip>
                        <el-tooltip class="box-item" effect="dark" content="将选中的测试用例标记为不复测" placement="top">
                            <el-button type="primary" plain @click="handlePatchRetest(false)">取消复测</el-button>
                        </el-tooltip>

                        <div style="margin-left: auto; display: flex; gap: 10px;">
                            <div>
                                <el-button icon="Setting" text bg @click="isSettingVisible = true">设置</el-button>
                                <!-- 设置弹窗 -->
                                <el-popover v-model:visible="isSettingVisible" width="180" trigger="manual"
                                    placement="bottom">
                                    <template #reference>
                                        <!-- 设置按钮作为触发器 -->
                                        <div style="display: inline-block;"></div>
                                    </template>
                                    <!-- 操作按钮 -->
                                    <div class="column-popper-title">
                                        <div
                                            style="display: flex; align-items: center; justify-content: space-between;">
                                            <el-checkbox :model-value="tableColumns.every(item => item.show)"
                                                :indeterminate="tableColumns.some(item => item.show) && tableColumns.some(item => !item.show)"
                                                label="列展示" @change="selectAllColumn" />
                                            <el-button text @click="resetColumns"
                                                style="margin-right: -10px;">重置</el-button>
                                        </div>
                                    </div>
                                    <!-- 列设置内容 -->
                                    <div class="column-content" style="max-height: 200px; overflow-y: auto;">
                                        <div class="column-item"
                                            v-for="column in tableColumns.filter(col => col.key !== 'selection')"
                                            :key="column.key">
                                            <el-checkbox v-model="column.show" :label="column.name"
                                                :disabled="column.disabled"></el-checkbox>
                                        </div>
                                    </div>

                                </el-popover>

                            </div>
                            <el-tooltip class="box-item" effect="dark" content="重置已添加的筛选条件" placement="top-start">
                                <el-button text bg @click="handleReset">重置</el-button>
                            </el-tooltip>
                            <filterButton @click="onFilterStatusChange" :count="filterCount" />
                            <el-button icon="Refresh" text bg @click="handleRefresh">刷新</el-button>
                        </div>

                    </div>

                    <div class="filter-container" v-if="showFilterContainer">
                        <el-input v-model="tc_form.name" placeholder="请输入用例名称" style="width: 400px;"
                            @keyup.enter="onFilter" clearable>
                            <template #append>
                                <el-button icon="Search" @click="onFilter"></el-button>
                            </template>
                        </el-input>
                        <el-tree-select v-model="tc_form.module" :data="modules" :props="{ label: 'name', value: 'm' }"
                            ref="moduleRef" placeholder="请选择所属模块" style="width: 400px;" clearable
                            :render-after-expand="false" node-key="m" show-checkbox check-strictly multiple
                            @check="onFilter">
                        </el-tree-select>
                        <el-select v-model="tc_form.execute_mode" placeholder="请选择执行方式" style="width: 400px;" multiple
                            clearable @change="onFilter">
                            <el-option label="自动化测试" value="AUTOMATED_EXECUTION"></el-option>
                            <el-option label="手动测试" value="MANUAL_EXECUTION"></el-option>
                            <el-option label="半自动化测试" value="SEMI_AUTOMATED_EXECUTION"></el-option>
                        </el-select>
                        <el-select v-model="tc_form.disabled" placeholder="执行状态" style="width: 400px;" clearable
                            @change="onFilter">
                            <el-option label="停用" value="1"></el-option>
                            <el-option label="启用" value="0"></el-option>
                        </el-select>
                        <el-select v-model="tc_form.result_two" placeholder="测试结果" style="width: 400px;" clearable
                            multiple @change="onFilter">
                            <el-option label="待执行" value="null"></el-option>
                            <el-option label="待判定" value="pending"></el-option>
                            <el-option label="PASS" value="1"></el-option>
                            <el-option label="NG" value="0"></el-option>
                            <el-option label="NA" value="2"></el-option>
                            <el-option label="NT" value="3"></el-option>
                        </el-select>
                        <el-select v-model="tc_form.status" placeholder="用例状态" style="width: 400px;" clearable
                            @change="onFilter">
                            <el-option label="待评审" value="PENDING"></el-option>
                            <el-option label="评审中" value="REVIEWING"></el-option>
                            <el-option label="评审通过" value="APPROVED"></el-option>
                            <el-option label="评审不通过" value="REJECTED"></el-option>
                            <el-option label="废弃" value="DEPRECATED"></el-option>
                        </el-select>
                    </div>

                    <el-table height="700" ref="tableRef" :data="testCaseData" stripe border style="width: 100%"
                        class="table-container" row-key="id">
                        <!-- 选择列 -->
                        <el-table-column v-if="tableColumns[0].show" type="selection" width="50" fixed="left" />

                        <!-- 序号列 -->
                        <el-table-column v-if="tableColumns[1].show" label="序号" width="60" align="center" fixed="left">
                            <template #default="{ row, $index }">
                                {{ $index + tc_form.pagesize * (tc_form.page - 1) + 1 }}
                            </template>
                        </el-table-column>

                        <!-- 用例ID列 -->
                        <el-table-column v-if="tableColumns[2].show" prop="number" label="用例ID" width="200"
                            align="center" fixed="left" />

                        <!-- 用例名称列 -->
                        <el-table-column v-if="tableColumns[3].show" prop="name" label="用例名称" width="200" align="center"
                            fixed="left">
                            <template #default="{ row }">
                                <el-link type="primary" @click="onDetail(row)" :underline="false">{{ row.name
                                }}</el-link>
                            </template>
                        </el-table-column>

                        <!-- 用例活动类型列 -->
                        <el-table-column v-if="tableColumns[4].show" label="用例活动类型" width="200" align="center">
                            <template #default="{ row }">
                                <span>{{ actionTypeMap[row.action_type] || row.action_type }}</span>
                            </template>
                        </el-table-column>

                        <!-- 用例版本列 -->
                        <el-table-column v-if="tableColumns[5].show" label="用例版本" width="100" align="center">
                            <template #default="{ row }">
                                V{{ row.version }}.0
                            </template>
                        </el-table-column>

                        <!-- 用例状态列 -->
                        <el-table-column v-if="tableColumns[6].show" label="用例状态" width="120" align="center">
                            <template #default="{ row }">
                                <el-tag v-if="row.status == 'PENDING'" type="primary">待评审</el-tag>
                                <el-tag v-else-if="row.status == 'REVIEWING'" type="warning">评审中</el-tag>
                                <el-tag v-else-if="row.status == 'CANCEL'" type="danger">已撤销</el-tag>
                                <el-tag v-else-if="row.status == 'APPROVED'" type="success">评审通过</el-tag>
                                <el-tag v-else-if="row.status == 'REJECTED'" type="danger">评审不通过</el-tag>
                                <el-tag v-else-if="row.status == 'DEPRECATED'" type="info">废弃</el-tag>
                                <el-tag v-else type="danger">未知</el-tag>
                            </template>
                        </el-table-column>

                        <!-- 执行状态列 -->
                        <el-table-column v-if="tableColumns[7].show" label="执行状态" width="100" align="center">
                            <template #default="{ row }">
                                <el-tag v-if="form.test_case_extra_args[row.id]?.disabled == true"
                                    type="warning">停用</el-tag>
                                <el-tag v-else type="primary">启用</el-tag>
                            </template>
                        </el-table-column>

                        <!-- 是否复测列 -->
                        <el-table-column v-if="tableColumns[8].show" label="是否复测" width="100" align="center">
                            <template #default="{ row }">
                                <el-tag v-if="form.test_case_extra_args[row.id]?.isRetest" type="warning">是</el-tag>
                                <el-tag v-else type="primary">否</el-tag>
                            </template>
                        </el-table-column>

                        <!-- 指定测试机台列 -->
                        <el-table-column v-if="tableColumns[9].show" label="指定测试机台" width="200" align="center">
                            <template #default="{ row }">
                                <span>{{ form.test_case_extra_args[row.id]?.machine_number || "" }}</span>
                            </template>
                        </el-table-column>

                        <!-- 执行方式列 -->
                        <el-table-column v-if="tableColumns[10].show" label="执行方式" width="120" align="center">
                            <template #default="{ row }">
                                <el-tag v-if="row.execute_mode == 'AUTOMATED_EXECUTION'" type="success">自动化测试</el-tag>
                                <el-tag v-else-if="row.execute_mode == 'MANUAL_EXECUTION'" type="success">手动测试</el-tag>
                                <el-tag v-else-if="row.execute_mode == 'SEMI_AUTOMATED_EXECUTION'"
                                    type="success">半自动化测试</el-tag>
                                <el-tag v-else type="danger">未知</el-tag>
                            </template>
                        </el-table-column>

                        <!-- 测试值列 -->
                        <el-table-column v-if="tableColumns[11].show" label="测试值" width="200" align="left"
                            header-align="center">
                            <template #default="{ row }">
                                <span style="white-space: pre-wrap;">
                                    {{ truncateLines(row.result?.value || "", 30) }}
                                </span>
                            </template>
                        </el-table-column>

                        <!-- 测试结果列 -->
                        <el-table-column v-if="tableColumns[12].show" label="测试结果" width="100" align="center">
                            <template #default="{ row }">
                                <template v-if="row.result?.result_two == null">
                                    <el-tag v-if="row.result?.id" type="warning">待判定</el-tag>
                                    <el-tag v-else type="info">待执行</el-tag>
                                </template>
                                <el-tag v-else-if="row.result?.result_two == 1" type="success">PASS</el-tag>
                                <el-tag v-else-if="row.result?.result_two == 0" type="danger">NG</el-tag>
                                <el-tag v-else-if="row.result?.result_two == 2" type="warning">NA</el-tag>
                                <el-tag v-else-if="row.result?.result_two == 3" type="info">NT</el-tag>
                            </template>
                        </el-table-column>

                        <!-- 测试记录列 -->
                        <el-table-column v-if="tableColumns[13].show" label="测试记录" width="100" align="center">
                            <template #default="{ row }">
                                <el-link type="primary" @click="viewRecords(row)" :underline="false">查看</el-link>
                            </template>
                        </el-table-column>

                        <!-- 操作列 -->
                        <el-table-column v-if="tableColumns[14].show" label="操作" min-width="250" fixed="right"
                            align="center">
                            <template #default="{ row }">
                                <div style="display: flex; justify-content: center; align-items: center">
                                    <el-button type="primary" size="small" @click="handleDelete(row)"
                                        :disabled="planStatus && !(planStatus == 'DEBUGGING' || planStatus == 'REJECTED')">删除</el-button>
                                    <el-button v-if="row.execute_mode == 'MANUAL_EXECUTION'" type="primary" size="small"
                                        @click="handleManualExec(row)">手动执行</el-button>
                                    <el-button v-if="row.execute_mode == 'SEMI_AUTOMATED_EXECUTION'" type="primary"
                                        size="small" @click="handleManualExec2(row)">手动判定</el-button>
                                    <el-button v-if="row.execute_mode == 'AUTOMATED_EXECUTION'" type="primary"
                                        size="small" @click="handleManualExec2(row)"
                                        :disabled="row.result?.result_two == null && !row.result?.id">手动复判</el-button>
                                    <el-button type="primary" size="small" @click="handlePush(row)">上报问题</el-button>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>


                    <div class="pagination-container" v-if="route.query.type == 'edit' || route.query.type == 'copy'">
                        <el-pagination :page-sizes="[1, 10, 15, 20, 25, 50, 100]"
                            layout="prev, pager, next, jumper, total, sizes" v-model:current-page="tc_form.page"
                            v-model:page-size="tc_form.pagesize" :total="tc_total" background @change="onPageChange" />
                    </div>
                </div>
            </el-collapse-item>
        </el-collapse>

        <el-dialog v-if="dialogAddVisible" v-model="dialogAddVisible" title="添加测试用例" width="1000"
            :close-on-click-modal="false">
            <AddTestCase :project_number="form.project_number" :action_type="form.test_type"
                :added_test_case_ids="tptc_ids" :durability="form.plan_use == 'DURABILITY_TEST' ? true : false"
                @confirm="onAddConfirm" @cancel="onAddCancel" />
        </el-dialog>

        <el-dialog v-if="dialogAssignMachineVisible" v-model="dialogAssignMachineVisible" title="指定机台" width="600"
            :close-on-click-modal="false">
            <assignMachine :test_case_ids="tableRef.getSelectionRows().map(item => item.id)"
                @confirm="onAssignMachineConfirm" @cancel="onAssignMachineCancel" />
        </el-dialog>

        <el-dialog v-if="dialogManualExecVisible" v-model="dialogManualExecVisible" title="手动执行" width="600"
            :close-on-click-modal="false">
            <ManualExec :manualExecContext="manualExecContext" @confirm="onManualExecConfirm"
                @cancel="onManualExecCancel" />
        </el-dialog>

        <el-dialog v-if="dialogManualExec2Visible" v-model="dialogManualExec2Visible" title="半自动化用例判定" width="600"
            :close-on-click-modal="false">
            <ManualExec2 :manualExecContext="manualExecContext" @confirm="onManualExec2Confirm"
                @cancel="onManualExec2Cancel" />
        </el-dialog>

        <el-dialog v-if="dialogManualExec3Visible" v-model="dialogManualExec3Visible" title="自动化用例备注" width="600"
            :close-on-click-modal="false">
            <ManualExec3 :manualExecContext="manualExecContext" @confirm="onManualExec3Confirm"
                @cancel="onManualExec3Cancel" />
        </el-dialog>

        <el-drawer v-model="drawerDetailVisible" :with-header="false" size="60%" :destroy-on-close="true">
            <TestCaseDetail :testCaseData="testCase" />
        </el-drawer>
    </div>


    <el-dialog v-if="dialogPushVisible" v-model="dialogPushVisible" width="920" :close-on-click-modal="false">
        <template #title>
            <div style="font-size:18px;font-weight: bold;">上报问题到产品开发平台</div>
        </template>
        <IssuePush :testCase="testCase" @confirm="onPushConfirm" @cancel="onPushCancel" />
    </el-dialog>

</template>


<script setup>
import { ref, onMounted, nextTick, reactive } from 'vue'
import http from '@/utils/http/http.js';
import { useRouter, useRoute } from 'vue-router';
import TestCaseDetail from '@/components/test_case.vue';
import AddTestCase from './addTestCase.vue';
import Projects from '@/components/projects.vue';
import assignMachine from './assignMachine.vue';
import Organizaiton from '@/components/Organization/index.vue';
import { ElMessageBox } from 'element-plus';
import ManualExec from './manualExec.vue';
import ManualExec2 from './manualExec2.vue';
import ManualExec3 from './manualExec3.vue';
import filterButton from '@/components/filterButton.vue';
import IssuePush from './push.vue';

const tableColumns = ref([
    { key: "selection", name: "选择", show: true, disabled: true }, // 选择列
    { key: "index", name: "序号", show: true, disabled: true }, // 序号列
    { key: "number", name: "用例ID", show: true, disabled: true }, // 用例ID列
    { key: "name", name: "用例名称", show: true }, // 用例名称列
    { key: "action_type", name: "用例活动类型", show: true }, // 用例活动类型列
    { key: "version", name: "用例版本", show: true }, // 用例版本列
    { key: "status", name: "用例状态", show: true }, // 用例状态列
    { key: "execute_status", name: "执行状态", show: true }, // 执行状态列
    { key: "isRetest", name: "是否复测", show: true }, // 是否复测列
    { key: "machine_number", name: "指定测试机台", show: true }, // 指定测试机台列
    { key: "execute_mode", name: "执行方式", show: true }, // 执行方式列
    { key: "test_value", name: "测试值", show: true }, // 测试值列
    { key: "test_result", name: "测试结果", show: true }, // 测试结果列
    { key: "test_record", name: "测试记录", show: true }, // 测试记录列
    { key: "operation", name: "操作", show: true }, // 操作列
]);


// 设置弹窗的显示状态
const isSettingVisible = ref(false);

// 全选或取消全选逻辑
const selectAllColumn = (checked) => {
    tableColumns.value.forEach((column) => {
        if (!column.disabled) { // 跳过禁用的列
            column.show = checked;
        }
    });
};

const saveLoading = ref(false);
const pushRelatedVersionsLoading = ref(false);


// 重置列设置
const resetColumns = () => {
    tableColumns.value.forEach((column) => {
        if (!column.disabled) { // 跳过禁用的列
            column.show = true;
        }
    });
};

const testProducts = ref([]);
const activeNames = ref(['1', '2']);
const projectRef = ref(null);
const testCaseData = ref([]);
const formRef = ref(null);
const router = useRouter();
const route = useRoute();
const productVersionRef = ref(null);
const projectVersionData = ref([]);
const projectVersionDataInit = ref([]);
const projectVersionV = ref(true);
const initProjectVersionData = ref([]);
const dialogAddVisible = ref(false);
const drawerDetailVisible = ref(false);
const dialogAssignMachineVisible = ref(false);
const tableRef = ref(null);
const test_types = ref([]);
const picInitData = ref([]);
const planStatus = ref(null);
const dialogManualExecVisible = ref(false);
const dialogManualExec2Visible = ref(false);
const dialogManualExec3Visible = ref(false);
const manualExecContext = ref({});
const picRef = ref(null);
const showFilterContainer = ref(false);
const modules = ref([]);
let actionTypeMap = ref({});
const reviewLoading = ref(false);
const testCase = ref({});
const publishLoading = ref(false);
const dialogPushVisible = ref(false);
let tptc_ids = [];
const updateLoading = ref(false);
const subVersionsRef = ref(null);

function onFilterStatusChange() {
    showFilterContainer.value = !showFilterContainer.value;
};
const filterCount = ref(0);

const form = ref({
    name: '',
    project_number: route.query.project_number || '',
    product_version: [],
    desc: '',
    m_version: '',
    sub_versions: [],
    abnormal_stop: '0',
    test_case_extra_args: {},
    test_type: '',
    test_type_name: '',
    plan_use: 'FULL_FUNCTIONALITY_TEST',
    pic_name: '',
    pic_email: '',
    p_start_time: null,
    p_end_time: null,
    sample_information: '',
    plan_type: '0',
    finish_notice: '0',
    test_product_id: null,
})

const rules = ref({
    name: [
        { required: true, message: '请输入计划名称', trigger: 'blur' }
    ],
    project_number: [
        { required: true, message: '请选择所属项目', trigger: 'change' }
    ],
    test_type: [
        { required: true, message: '请选择测试类型', trigger: 'change' }
    ],
    plan_use: [
        { required: true, message: '请选择计划用途', trigger: 'change' }
    ],
    p_start_time: [
        { required: true, message: '请选择计划开始时间', trigger: 'change' }
    ],
    p_end_time: [
        { required: true, message: '请选择计划结束时间', trigger: 'change' }
    ],
    m_version: [
        { required: true, message: '请选择主版本', trigger: 'change' }
    ],
    pic_email: [
        { required: true, message: '请选择负责人', trigger: 'change' }
    ],
    plan_type: [
        { required: true, message: '请选择计划类型', trigger: 'change' }
    ],
})

const tc_total = ref(0);
let tc_form = reactive({
    result_two: [],
    page: 1,
    pagesize: 10,
})

function update_tc_table() {
    const id = route.query.id;
    http.get(`/v2/test_plans/${id}/test_cases`, { params: tc_form }).then(res => {
        let data = res.data.data;
        testCaseData.value = data.test_cases;
        tc_total.value = data.count;
    }).catch(err => {
        console.log(err);
    });

    filterCount.value = Object.keys(tc_form).filter(key => !['page', 'pagesize'].includes(key)).reduce((count, key) => {
        if (tc_form[key] == '' || tc_form[key] == undefined || tc_form[key] == null || tc_form[key].length == 0) {
            return count;
        } else {
            return count + 1;
        }
    }, 0)
};

function handleReset() {
    tc_form = reactive({
        page: 1,
        pagesize: 10,
    });
    update_tc_table();
};

function onPageChange() {
    update_tc_table();
};

function onFilter() {
    form.value.page = 1;
    form.value.pagesize = 10;
    update_tc_table();
};

function handleRefresh() {
    update_tc_table();
};

function onProjectChange() {
    projectVersionV.value = false;
    nextTick(() => {
        projectVersionV.value = true;
    });
    form.value.m_version = '';
    form.value.product_version = [];
    form.value.test_type = '';
    updateTestTypes();
};

const onSaveCreate = (sync = false, callback = undefined) => {
    formRef.value.validate((valid) => {
        if (valid) {
            let data = {
                ...form.value,
                test_cases: tptc_ids,
                test_case_extra_args: JSON.stringify(form.value.test_case_extra_args),
            }

            let pv = data.product_version.map(item => {
                return productVersionRef.value.getNode(item)?.data;
            });
            data.product_version = JSON.stringify(pv);

            let sub_versions = data.sub_versions.map(item => {
                return subVersionsRef.value.getNode(item)?.data;
            });
            data.sub_versions = JSON.stringify(sub_versions);

            saveLoading.value = true;
            http.post('/v2/test_plans', data).then(res => {
                if (sync == true) {
                    http.post('/v2/test_plans/sync', { id: res.data.data.id }).then(res => {
                        ElMessage({
                            message: '保存并同步成功.',
                            type: 'success',
                        });
                        router.push({ path: '/test_plans_v2/list' });
                    }).catch(err => {
                        ElMessageBox.alert(err.response.data.msg, '提交失败', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'error',
                        })
                    }).finally(() => {
                        saveLoading.value = false;
                        callback && callback();
                    });
                } else {
                    saveLoading.value = false;
                    ElMessage({
                        message: '保存成功.',
                        type: 'success',
                    });
                    router.push({ path: '/test_plans_v2/list' });
                };
            }).catch(err => {
                saveLoading.value = false;
                ElMessageBox.alert(
                    err.response.data.msg,
                    '保存失败',
                    {
                        confirmButtonText: '确定',
                        type: 'warning',
                    }
                )
            });
        } else {
            return false;
        }
    });
}

const onSaveEdit = (sync = false, callback = undefined) => {
    formRef.value.validate((valid) => {
        if (valid) {

            let data = {
                ...form.value,
                test_cases: tptc_ids,
                test_case_extra_args: JSON.stringify(form.value.test_case_extra_args),
            }

            let pv = data.product_version.map(item => {
                let i = initProjectVersionData.value.find(item2 => item2.value == item);
                if (i) {
                    return i;
                }
                return productVersionRef.value.getNode(item).data;
            });
            data.product_version = JSON.stringify(pv);

            let sub_versions = data.sub_versions.map(item => {
                let i = initProjectVersionData.value.find(item2 => item2.value == item);
                if (i) {
                    return i;
                }
                return subVersionsRef.value.getNode(item).data;
            });
            data.sub_versions = JSON.stringify(sub_versions);

            http.put(`/v2/test_plans/${route.query.id}`, data).then(res => {
                if (sync == true) {
                    http.post('/v2/test_plans/sync', { id: res.data.data.id }).then(res => {
                        ElMessage({
                            message: '保存并同步成功.',
                            type: 'success',
                        });
                        router.push({ path: '/test_plans_v2/list' });
                    }).catch(err => {
                        ElMessageBox.alert(err.response.data.msg, '提交失败', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'error',
                        })
                    }).finally(() => {
                        callback && callback();
                    });
                } else {
                    ElMessage({
                        message: '保存成功.',
                        type: 'success',
                    });
                    router.push({ path: '/test_plans_v2/list' });
                };
            }).catch(err => {
                ElMessageBox.alert(
                    err.response.data.msg,
                    '保存失败',
                    {
                        confirmButtonText: '确定',
                        type: 'warning',
                    }
                )
            });
        } else {
            return false;
        }
    });
}

const onSave = (sync = false, callback = undefined) => {
    let pInfo = projectRef.value.getProjectInfo(form.value.project_number);
    form.value.project_id = pInfo?.id;
    form.value.project_name = pInfo?.name;

    form.value.test_type_name = test_types.value.find(item => item.remark == form.value.test_type)?.label;

    let pic = picRef.value.getNode(form.value.pic_email);

    form.value.pic_name = pic?.label;

    if (!form.value.pic_name) {
        ElMessageBox.alert(
            '负责人不能为空，请选择负责人！',
            '警告',
            {
                confirmButtonText: '确定',
                type: 'warning',
            }
        )
        return;
    }

    // if (tptc_ids.length == 0) {
    //     ElMessageBox.alert(
    //         '测试用例为空，请添加测试用例！',
    //         '警告',
    //         {
    //             confirmButtonText: '确定',
    //             type: 'warning',
    //         }
    //     )
    //     return;
    // }

    if (route.query.type === 'edit') {
        onSaveEdit(sync, callback);
    } else {
        onSaveCreate(sync, callback);
    }
}

const onPublish = () => {
    ElMessageBox.confirm('是否发布测试计划？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        let f = true;
        testCaseData.value.forEach(item => {
            if (item.status != 'APPROVED') {
                f = false;
            }
        });
        if (!f) {
            ElMessageBox.alert(
                '测试用例未全部评审通过，请评审通过后再发布！',
                '警告',
                {
                    confirmButtonText: '确定',
                    type: 'warning',
                }
            )
            return;
        }

        publishLoading.value = true;
        onSave(true, () => {
            publishLoading.value = false;
        });

    }).catch(() => {
        return;
    });
}
const onPushCancel = () => {
    dialogPushVisible.value = false
}
const handleAdd = () => {
    dialogAddVisible.value = true;
}

const handlePatchDelete = () => {
    ElMessageBox.confirm(
        '确定删除吗?',
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        const selectedRows = tableRef.value.getSelectionRows();
        if (selectedRows.length == 0) {
            ElMessageBox.alert(
                '没有用例被选择，请选择需要删除的测试用例！',
                '警告',
                {
                    confirmButtonText: '确定',
                    type: 'warning',
                }
            )
            return;
        };
        tptc_ids = tptc_ids.filter(item => !selectedRows.map(item => item.id).includes(item));

        if (route.query.type === 'edit') {
            const id = route.query.id;
            http.post(`/v2/test_plans/${id}/test_cases/delete`, { test_case_ids: selectedRows.map(item => item.id) }).then(res => {
                update_tc_table();
                ElMessage({
                    message: '删除成功.',
                    type: 'success',
                });
            }).catch(err => {
                ElMessageBox.alert(
                    err.response.data.msg,
                    '删除失败',
                    {
                        confirmButtonText: '确定',
                        type: 'warning',
                    }
                )
            });
        } else {
            testCaseData.value = testCaseData.value.filter(item => !selectedRows.includes(item));
        }

    }).catch(() => {
        ElMessage({
            type: 'info',
            message: '已取消删除'
        });
    });
}

const handleUpdate = () => {
    if (route.query.type === 'edit') {
        const id = route.query.id;
        updateLoading.value = true;
        http.post(`/v2/test_plans/${id}/test_cases/update`).then(res => {
            update_tc_table()
            ElMessage({
                message: '更新成功.',
                type: 'success',
            });
        }).catch(err => {
            ElMessageBox.alert(
                err.response.data.msg,
                '更新失败',
                {
                    confirmButtonText: '确定',
                    type: 'warning',
                }
            )
        }).finally(() => {
            updateLoading.value = false;
        });
    }
}

const handleDelete = (row) => {
    ElMessageBox.confirm(
        '确定删除吗?',
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        tptc_ids = tptc_ids.filter(item => item !== row.id);

        if (route.query.type === 'edit') {
            const id = route.query.id;
            http.post(`/v2/test_plans/${id}/test_cases/delete`, { test_case_ids: [row.id] }).then(res => {
                update_tc_table();
                ElMessage({
                    message: '删除成功.',
                    type: 'success',
                });
            }).catch(err => {
                ElMessageBox.alert(
                    err.response.data.msg,
                    '删除失败',
                    {
                        confirmButtonText: '确定',
                        type: 'warning',
                    }
                )
            });
        } else {
            testCaseData.value = testCaseData.value.filter(item => item.id !== row.id);
        }

    }).catch(() => {
        ElMessage({
            type: 'info',
            message: '已取消删除'
        });
    });
};

const handleManualExec = (row) => {
    manualExecContext.value = {
        test_plan_id: route.query.id,
        test_case_id: row.id,
        test_case_version: row.version,
        value: row.result.value,
        result: row.result.result,
        result_two: row.result.result_two,
        file: row.result.file,
        remark: row.result.remark,
    };
    dialogManualExecVisible.value = true;
}

const handleManualExec2 = (row) => {
    manualExecContext.value = {
        test_plan_id: route.query.id,
        test_case_id: row.id,
        test_case_version: row.version,
        value: row.result.value,
        result: row.result.result,
        result_two: row.result.result_two,
        remark: row.result.remark,
    };
    dialogManualExec2Visible.value = true;
}

const handleManualExec3 = (row) => {
    manualExecContext.value = {
        test_plan_id: route.query.id,
        test_case_id: row.id,
        test_case_version: row.version,
        value: row.result.value,
        result: row.result.result,
        result_two: row.result.result_two,
        remark: row.result?.remark,
    };
    dialogManualExec3Visible.value = true;
}

const onManualExecConfirm = (data) => {
    dialogManualExecVisible.value = false;
    let item = testCaseData.value.find(item => item.id == data.test_case_id);
    item.result.result = data.result;
    item.result.value = data.value;
    item.result.result_two = data.result_two;
}

const onManualExecCancel = () => {
    dialogManualExecVisible.value = false;
}

const onManualExec2Confirm = (data) => {
    dialogManualExec2Visible.value = false;
    let item = testCaseData.value.find(item => item.id == data.test_case_id);
    item.result.result = data.result;
    item.result.value = data.value;
    item.result.result_two = data.result_two;
}

const onManualExec2Cancel = () => {
    dialogManualExec2Visible.value = false;
}

const onManualExec3Confirm = (data) => {
    dialogManualExec3Visible.value = false;
    let item = testCaseData.value.find(item => item.id == data.test_case_id);
    item.result.result = data.result;
    item.result.value = data.value;
    item.result.result_two = data.result_two;
}

const onManualExec3Cancel = () => {
    dialogManualExecVisible.value = false;
}

const handlePatchAssignMachine = () => {
    const selectedRows = tableRef.value.getSelectionRows();
    if (selectedRows.length == 0) {
        ElMessageBox.alert(
            '没有用例被选择，请选择需要指定机台的测试用例！',
            '警告',
            {
                confirmButtonText: '确定',
                type: 'warning',
            }
        )
        return;
    };

    dialogAssignMachineVisible.value = true;
}

const onAssignMachineCancel = () => {
    dialogAssignMachineVisible.value = false;
}

const onAssignMachineConfirm = (data) => {
    dialogAssignMachineVisible.value = false;

    data.test_case_ids.forEach(item => {
        if (!form.value.test_case_extra_args[item]) {
            form.value.test_case_extra_args[item] = {};
        }
        form.value.test_case_extra_args[item]['machine_number'] = data.machine_number;
    })
}

const handlePatchDiable = () => {
    const selectedRows = tableRef.value.getSelectionRows();

    if (selectedRows.length == 0) {
        ElMessageBox.alert(
            '没有用例被选择，请选择需要停用的测试用例！',
            '警告',
            {
                confirmButtonText: '确定',
                type: 'warning',
            }
        )
        return;
    };

    selectedRows.forEach(item => {
        if (!form.value.test_case_extra_args[item.id]) {
            form.value.test_case_extra_args[item.id] = {};
        }
        form.value.test_case_extra_args[item.id]['disabled'] = true;
    });
}

const handlePatchEnable = () => {
    const selectedRows = tableRef.value.getSelectionRows();

    if (selectedRows.length == 0) {
        ElMessageBox.alert(
            '没有用例被选择，请选择需要启用的测试用例！',
            '警告',
            {
                confirmButtonText: '确定',
                type: 'warning',
            }
        )
        return;
    };

    selectedRows.forEach(item => {
        if (!form.value.test_case_extra_args[item.id]) {
            form.value.test_case_extra_args[item.id] = {};
        }
        form.value.test_case_extra_args[item.id]['disabled'] = false;
    });
}

const handlePatchReview = () => {
    const selectedRows = tableRef.value.getSelectionRows();
    let ids = selectedRows.map(item => {
        if (item.status == "PENDING") {
            return item.id;
        }
    }).filter(item => item != undefined);

    if (ids.length == 0) {
        ElMessageBox.alert(
            '没有需要评审的测试用例，请选择待评审的测试用例！',
            '警告',
            {
                confirmButtonText: '确定',
                type: 'warning',
            }
        )
        return;
    }
    reviewLoading.value = true;
    http.post('/test_cases/patch_sync', { project_number: form.value.project_number, category: form.value.test_type, ids: ids }, { timeout: 150000 }).then(res => {
        ElMessage.success('评审成功');
    }).catch(err => {
        ElMessageBox.alert(
            err.response.data.msg,
            '警告',
            {
                confirmButtonText: '确定',
                type: 'warning',
            }
        )
    }).finally(() => {
        reviewLoading.value = false;
    });
}

const handlePatchRetest = (flag) => {
    const selectedRows = tableRef.value.getSelectionRows();

    if (selectedRows.length == 0) {
        ElMessageBox.alert(
            '没有用例被选择，请选择需要操作的测试用例！',
            '警告',
            {
                confirmButtonText: '确定',
                type: 'warning',
            }
        )
        return;
    };

    selectedRows.forEach(item => {
        if (!form.value.test_case_extra_args[item.id]) {
            form.value.test_case_extra_args[item.id] = {};
        }
        form.value.test_case_extra_args[item.id]['isRetest'] = flag;
    });
};

const handleSyncTestPlanResult = () => {
    if (planStatus.value != 'RUNNING' && planStatus.value != 'COMPLETED') {
        ElMessageBox.alert(
            '此测试计划未同步到产品开发平台，不能推送测试结果！',
            '提示',
            {
                confirmButtonText: '确定',
                type: 'warning',
            }
        )
        return;
    }

    http.post('/v2/test_plans/sync_test_result', { test_plan_id: route.query.id }).then(res => {
        ElMessage.success('评审成功');
    }).catch(err => {
        ElMessageBox.alert(
            err.response.data.msg,
            '警告',
            {
                confirmButtonText: '确定',
                type: 'warning',
            }
        )
    });
}

const handlePushRelatedVersions = () => {
    pushRelatedVersionsLoading.value = true;

    http.post('/v2/test_plans/push_related_versions', { test_plan_id: route.query.id }).then(res => {
        ElMessage.success('推送成功');
    }).catch(err => {
        ElMessageBox.alert(
            err.response.data.msg,
            '警告',
            {
                confirmButtonText: '确定',
                type: 'warning',
            }
        )
    }).finally(() => {
        pushRelatedVersionsLoading.value = false;
    });

}

const onAddConfirm = (data) => {
    dialogAddVisible.value = false;

    const newItems = data.filter(item => tptc_ids.findIndex(i => i === item.id) === -1);
    tptc_ids = tptc_ids.concat(newItems.map(item => item.id));

    if (newItems.length == 0) {
        ElMessage({
            message: '没有新的测试用例需要添加.',
            type: 'info',
        });
        return;
    }

    if (route.query.type === 'edit') {
        const id = route.query.id;
        http.post(`/v2/test_plans/${id}/test_cases/add`, { test_case_ids: newItems.map(item => item.id) }).then(res => {
            update_tc_table();
            ElMessage({
                message: '添加成功.',
                type: 'success',
            });
        }).catch(err => {
            ElMessageBox.alert(
                err.response.data.msg,
                '添加失败',
                {
                    confirmButtonText: '确定',
                    type: 'warning',
                }
            )
        });
    } else {
        testCaseData.value = testCaseData.value.concat(newItems);
    }


}

const onAddCancel = () => {
    dialogAddVisible.value = false;
}

function updateOnEditTestPlan() {
    const id = route.query.id;
    if (id) {
        http.get(`/v2/test_plans/${id}`).then(res => {
            let data = res.data.data;
            form.value = {
                name: data.name,
                project_number: data.project_number,
                desc: data.desc,
                m_version: data.m_version,
                sub_versions: data.sub_versions.filter(item => item != null).map(item => item.id),
                product_version: data.product_version.filter(item => item != null).map(item => item.id),
                abnormal_stop: data.abnormal_stop ? '1' : '0',
                finish_notice: data.finish_notice ? '1' : '0',
                test_case_extra_args: data.test_case_extra_args,
                test_type: data.test_type,
                test_type_name: data.test_type_name,
                plan_use: data.plan_use,
                plan_type: String(data.plan_type),
                pic_name: data.pic_name,
                pic_email: data.pic_email,
                p_start_time: data.p_start_time,
                p_end_time: data.p_end_time,
                sample_information: data.sample_information,
                test_product_id: data.test_product_id,
            }
            picInitData.value = [{ label: data.pic_name, value: data.pic_email }];
            initProjectVersionData.value = data.product_version.filter(item => item != null).concat(data.sub_versions.filter(item => item != null));
            planStatus.value = data.status;
            tptc_ids = data.test_cases.map(item => item.id);

            updateTestTypes();

        }).catch(err => {
            console.log(err);
        });

        http.get(`/v2/test_plans/${id}/test_cases`).then(res => {
            let data = res.data.data;
            testCaseData.value = data.test_cases;
            tc_total.value = data.count;
        }).catch(err => {
            console.log(err);
        });
    }
}

function updateOnCopyTestPlan() {
    const id = route.query.id;
    if (id) {
        http.get(`/v2/test_plans/${id}`).then(res => {
            let data = res.data.data;
            form.value = {
                name: data.name + '_Copy',
                project_number: data.project_number,
                desc: data.desc,
                m_version: data.m_version,
                product_version: data.product_version.filter(item => item != null).map(item => item.id),
                abnormal_stop: data.abnormal_stop ? '1' : '0',
                finish_notice: data.finish_notice ? '1' : '0',
                test_case_extra_args: data.test_case_extra_args,
                test_type: data.test_type,
                test_type_name: data.test_type_name,
                plan_use: data.plan_use,
                plan_type: String(data.plan_type),
                pic_name: data.pic_name,
                pic_email: data.pic_email,
                p_start_time: data.p_start_time,
                p_end_time: data.p_end_time,
                sample_information: data.sample_information,
            }
            picInitData.value = [{ label: data.pic_name, value: data.pic_email }];
            initProjectVersionData.value = data.product_version.filter(item => item != null);
            tptc_ids = data.test_cases.map(item => item.id);

            updateTestTypes();

        }).catch(err => {
            console.log(err);
        });

        http.get(`/v2/test_plans/${id}/test_cases`).then(res => {
            let data = res.data.data;
            testCaseData.value = data.test_cases;
            tc_total.value = data.count;
        }).catch(err => {
            console.log(err);
        });
    }
}

function loadVersions(node, resolve) {
    if (node.level === 0) {
        return resolve(projectVersionDataInit.value);
    }
    if (node.level === 1) {
        http.get('/issues/project_version_number',
            { params: { type: node.data.code, project_number: form.value.project_number } }).then(res => {
                if (res.data.err_code != 0) {
                    resolve([{ label: '无数据', value: '无数据', isLeaf: true, disabled: true }]);
                    return;
                }
                let data = res.data.data.records;
                data = data.map(item => {
                    item.label = item.name;
                    item.value = item.id;
                    item.type_name = node.data.label;
                    item.isLeaf = true;
                    return item;
                });
                resolve(data);
                if (data.length == 0) {
                    resolve([{ label: '无数据', value: '无数据', isLeaf: true, disabled: true }]);
                }
            });
    }
}

function onBack() {
    router.push({ path: '/test_plans_v2' });
}

function onDetail(row) {
    testCase.value = row;
    drawerDetailVisible.value = true;
};

function viewRecords(row) {
    router.push({ path: "/test_records_v2/items/", query: { id: row.result.exec_id, test_case_id: row.result.test_case_id } });
}

function updateTestTypes() {
    if (!form.value.project_number) {
        test_types.value = [];
        return;
    }
    http.get('/test_cases/test_types', { params: { project_number: form.value.project_number } }).then(res => {
        test_types.value = res.data.data;
    });
}

onMounted(() => {
    http.get('/issues/project_version_type').then(res => {
        let data = res.data.data.dictList;
        data = data.map(item => {
            item.value = item.id;
            item.isLeaf = false;
            return item;
        });
        projectVersionDataInit.value = data;
        projectVersionData.value = data;
    });

    updateTestTypes();

    if (route.query.type === 'edit') {
        updateOnEditTestPlan();
    } else if (route.query.type === 'copy') {
        updateOnCopyTestPlan();
    }


    http.get('/functions').then(res => {
        let data = res.data.data.results;
        data.forEach(item => {
            item.m = item.number;
            if (item.children) {
                item.children.forEach(item2 => {
                    item2.m = item.number + '-' + item2.number;
                    if (item2.children) {
                        item2.children.forEach(item3 => {
                            item3.m = item.number + '-' + item2.number + '-' + item3.number;
                        });
                    }
                });
            }
        });
        modules.value = data;
    });

    http.get('/test_m/test_case_types', { params: { pagesize: 10000 } }).then(res => {
        res.data.data.results.forEach(item => {
            actionTypeMap.value[item.number] = item.name;
        });

    });

     http.get('/test_products', { params: { page: 1, pageSize: 100000 } }).then(res => {
        testProducts.value = res.data.data.results;
    });

})

function handlePush(row) {
    testCase.value = row;
    dialogPushVisible.value = true;
};

function truncateLines(str, maxLength) {
    // 将字符串按换行符分割成数组
    const lines = str.split('\n');
    // 处理每一行，如果超过 maxLength，则截断并添加省略号
    const truncatedLines = lines.map(line => {
        if (line.length > maxLength) {
            // 截断并添加省略号
            return line.substring(0, maxLength - 3) + '...'; // 保留空间给省略号
        }
        return line; // 如果不超过 maxLength，直接返回原行
    });
    // 将处理后的行重新用换行符连接
    return truncatedLines.join('\n');
}
</script>


<style lang="scss" scoped>
.top-tool-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: white;
    height: 50px;
}

.tool-bar-container {
    display: flex;
    justify-content: start;
    align-items: center;
}

.filter-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    margin-bottom: 10px;

    .el-input,
    .el-select {
        width: 50%;
        max-width: 300px;
        margin-right: 10px;
    }
}

:deep(.el-collapse-item__arrow) {
    order: -1;
    margin: 0 8px 0 0;
}

:deep(.el-collapse-item__header) {
    font-size: 16px;
}

::v-deep .el-collapse-item__header {
    font-weight: bold;
    font-size: 15px;
}

.column-popper-title {
    border-bottom: 1px solid #ebeef5;
}

/* 自定义滚动条样式 */
.column-content::-webkit-scrollbar {
    width: 6px;
    /* 滚动条宽度 */
}

.column-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    /* 轨道背景色 */
    border-radius: 4px;
    /* 轨道圆角 */
}

/* 滚动条滑块默认样式（浅色） */
.column-content::-webkit-scrollbar-thumb {
    background: #e4e3e3;
    /* 浅色 */
    border-radius: 4px;
    /* 滑块圆角 */
}

/* 滚动条滑块悬停样式（深色） */
.column-content::-webkit-scrollbar-thumb:hover {
    background: #c7c7c7;
    /* 深色 */
}
</style>