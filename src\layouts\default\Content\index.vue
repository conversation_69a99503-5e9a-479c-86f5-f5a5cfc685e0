<template>
    <section style="width: 100%; height: 100%;">
        <router-view v-slot="{ Component, route }">
            <template v-if="route.meta.keepAlive">
                <keep-alive>
                    <component :is="Component" :key="route.path" />
                </keep-alive>
            </template>
            <template v-else>
                <component :is="Component" :key="route.fullPath" />
            </template>
        </router-view>
    </section>
</template>

<script setup>

</script>


<style lang="scss" scoped>
/* fade-transform */
.fade-transform-leave-active,
.fade-transform-enter-active {
    transition: all .5s;
}

.fade-transform-enter {
    opacity: 0;
    transform: translateX(-30px);
}

.fade-transform-leave-to {
    opacity: 0;
    transform: translateX(30px);
}
</style>