<template>
  <div ref="containerRef" class="pin-diagram-container">
    <!-- 动态计算viewBox以适配所有内容 -->
    <svg
      :viewBox="`0 0 ${svgWidth} ${svgHeight}`"
      :style="svgStyle"
      preserveAspectRatio="xMidYMid meet"
    >
      <!-- 定义渐变和阴影效果 -->
      <defs>
        <linearGradient id="chipGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#B3D9FF;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#B3D9FF;stop-opacity:1" />
        </linearGradient>
        <filter id="chipShadow" x="-20%" y="-20%" width="140%" height="140%">
          <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000" flood-opacity="0.3"/>
        </filter>
      </defs>

      <!-- 芯片主体和引脚 -->
      <g :transform="`translate(${chipAreaX}, ${chipAreaY})`">
        <!-- 芯片主体 -->
        <rect
          :x="chipX"
          :y="chipY"
          :width="chipSize"
          :height="chipSize"
          rx="12"
          ry="12"
          fill="url(#chipGradient)"
          stroke="#fff"
          stroke-width="3"
          filter="url(#chipShadow)"
        />

        <!-- 1脚标识圆点 -->
        <circle
          :cx="chipX + chipSize / (pinsPerSide + 1) * 1"
          :cy="chipY + chipSize * 0.03"
          :r="chipSize * 0.01"
          fill="#fff"
          stroke="#333"
          stroke-width="0.5"
          opacity="0.95"
        />

        <text
          :x="chipX + chipSize / 2"
          :y="chipY + chipSize / 2"
          text-anchor="middle"
          dominant-baseline="middle"
          :font-size="chipModelFontSize"
          font-weight="bold"
          fill="#fff"
        >
          {{ chipModel }}
        </text>

        <!-- 引脚 -->
        <template v-for="(pin, index) in pinPositions" :key="index">
          <g
            class="pin-group"
            :transform="`translate(${pin.x}, ${pin.y})`"
            @click="onPinClick(pin)"
          >
            <rect
              :x="pin.rectX"
              :y="pin.rectY"
              :width="pin.rectW"
              :height="pin.rectH"
              rx="2"
              ry="2"
              :fill="getPinColor(pin)"
              stroke="#999"
              stroke-width="1"
            />
            <text
              :x="pin.rectX + pin.rectW / 2"
              :y="pin.rectY + pin.rectH / 2"
              text-anchor="middle"
              dominant-baseline="middle"
              :font-size="pinFontSize"
              fill="#000"
            >
              {{ getPinNumber(pin) }}
            </text>
          </g>
        </template>
      </g>

      <!-- 引脚类型图例（侧边显示） -->
      <g v-if="showLegend && legendItems.length > 0" :transform="`translate(${legendX}, ${legendY})`">
        <!-- 图例背景框 -->
        <rect
          x="0"
          y="0"
          :width="legendBoxWidth"
          :height="legendBoxHeight"
          rx="8"
          ry="8"
          fill="#f8f8f8"
          stroke="#ddd"
          stroke-width="1"
        />
        <!-- 图例标题 -->
        <text
          x="15"
          y="25"
          :font-size="legendTitleFontSize"
          font-weight="bold"
          fill="#333"
        >
          引脚类型图例 ({{ legendItems.length }})
        </text>
        <!-- 图例项目 -->
        <template v-for="(item, idx) in legendItems" :key="item.type">
          <g :transform="`translate(${15}, ${40 + idx * legendItemSpacing})`">
            <rect
              x="0"
              y="0"
              :width="legendIconSize"
              :height="legendIconSize"
              :fill="item.color"
              stroke="#999"
              stroke-width="1"
              rx="3"
            />
            <text
              :x="legendIconSize + 8"
              :y="legendIconSize / 2"
              :font-size="legendTextFontSize"
              fill="#333"
              alignment-baseline="middle"
            >
              {{ item.type }}
            </text>
          </g>
        </template>
      </g>


    </svg>
  </div>
</template>

<script setup>
import { computed, ref, watch, onMounted, onUnmounted, nextTick } from 'vue'

const props = defineProps({
  pinCount: {
    type: Number,
    required: true,
    validator: (val) => val % 4 === 0 && val > 0,
  },
  chipModel: {
    type: String,
    default: '',
  },
  pins: {
    type: Array,
    default: () => [],
  },
  pinSize: {
    type: Number,
    default: 1.0,
    validator: (val) => val > 0.5 && val <= 2.0,
  },
  showLegend: {
    type: Boolean,
    default: true,
  },
  typeStyleConfig: {
    type: Object,
    default: () => ({}),
  },
  highlightedPin: {
    type: [String, Number],
    default: null
  }
})

// 容器引用和尺寸检测
const containerRef = ref(null)
const containerSize = ref({ width: 800, height: 600 })
const resizeObserver = ref(null)

// 颜色映射 - 基于传入的typeStyleConfig动态生成
const pinColors = computed(() => {
  if (Object.keys(props.typeStyleConfig).length === 0) {
    // 如果没有传入配置，使用默认配置
    return {
      Power: '#EF9A9A',
      GPIO: '#A5D6A7',
      UART: '#FFCC80',
      SPI: '#CE93D8',
      IIC: '#90CAF9',
      ADC: '#FFAB91',
      PWM: '#B39DDB',
      EXIT: '#FFD54F',
      CAN: '#BCAAA4',
      DEFAULT: '#CFD8DC',
    };
  }

  // 基于typeStyleConfig生成颜色映射
  const colors = {};
  Object.entries(props.typeStyleConfig).forEach(([type, config]) => {
    colors[type] = config.color;
  });
  return colors;
});

// 图例相关计算
const legendItems = computed(() => {
  if (!props.showLegend) return []

  // 收集所有引脚支持的类型，而不仅仅是主要类型
  const typesUsed = new Set()

  props.pins.forEach(pin => {
    // 添加主要类型
    if (pin.pinType) {
      typesUsed.add(pin.pinType)
    }

    // 添加所有支持的类型
    if (pin.supportedTypes && Array.isArray(pin.supportedTypes)) {
      pin.supportedTypes.forEach(type => {
        if (type) {
          typesUsed.add(type)
        }
      })
    }
  })

  // 确保至少有一个类型
  if (typesUsed.size === 0) {
    typesUsed.add('GPIO')
  }

  // 按类型名称排序，让常用类型排在前面
  const typeOrder = ['GPIO', 'ADC', 'PWM', 'UART', 'SPI', 'IIC', 'EXIT', 'CAN', 'Power']
  const sortedTypes = Array.from(typesUsed).sort((a, b) => {
    const indexA = typeOrder.indexOf(a)
    const indexB = typeOrder.indexOf(b)
    if (indexA === -1 && indexB === -1) return a.localeCompare(b)
    if (indexA === -1) return 1
    if (indexB === -1) return -1
    return indexA - indexB
  })

  return sortedTypes.map(type => ({
    type,
    color: pinColors.value[type] || pinColors.value.DEFAULT
  }))
})

// 图例尺寸计算（侧边布局）
const legendIconSize = computed(() => Math.max(12, 14 * adaptiveScale.value))
const legendItemSpacing = computed(() => Math.max(20, 24 * adaptiveScale.value))
const legendTitleFontSize = computed(() => Math.max(10, 12 * adaptiveScale.value))
const legendTextFontSize = computed(() => Math.max(8, 10 * adaptiveScale.value))

const legendBoxWidth = computed(() => {
  if (legendItems.value.length === 0) return 0
  return Math.max(120, 140 * adaptiveScale.value)
})

const legendBoxHeight = computed(() => {
  if (legendItems.value.length === 0) return 0
  return 50 + legendItems.value.length * legendItemSpacing.value
})

// 布局参数（侧边图例）
const chipAreaMargin = 10 // 芯片区域的边距（增加上下边距为50px）
const legendToChipSpacing = computed(() => Math.max(40, 60 * adaptiveScale.value)) // 图例与芯片之间的间距（增加间距）

// 芯片区域计算（居中布局）
const chipAreaX = computed(() => {
  const pinHeight = 25 * adaptiveScale.value * props.pinSize
  return chipAreaMargin + pinHeight // 为左侧引脚预留空间
})
const chipAreaY = computed(() => {
  // 确保顶部引脚不会超出上边距
  const pinHeight = 25 * adaptiveScale.value * props.pinSize
  return chipAreaMargin + pinHeight
})

// 图例位置计算（右侧）
const legendX = computed(() => {
  const chipAreaWidth = chipSize.value + pinOffset.value * 2
  return chipAreaX.value + chipAreaWidth + legendToChipSpacing.value
})
const legendY = computed(() => {
  const chipAreaHeight = chipSize.value + pinOffset.value * 2
  const chipCenterY = chipAreaY.value + chipAreaHeight / 2
  return chipCenterY - legendBoxHeight.value / 2
})

// 自适应尺寸计算
const adaptiveScale = computed(() => {
  if (!containerSize.value.width || !containerSize.value.height) return 1

  // 基础SVG尺寸（不包含自适应缩放）
  const baseWidth = 600
  const baseHeight = 500

  // 计算容器可用空间（留出边距）
  const availableWidth = containerSize.value.width - 40
  const availableHeight = containerSize.value.height - 40

  // 计算缩放比例，取较小值以确保完全适配
  const scaleX = availableWidth / baseWidth
  const scaleY = availableHeight / baseHeight

  return Math.min(scaleX, scaleY, 2) // 最大不超过2倍
})

// 芯片尺寸计算（基于自适应缩放）
const baseChipSize = computed(() => 300 * adaptiveScale.value)
const chipSize = computed(() => baseChipSize.value * props.pinSize)
const pinOffset = computed(() => 10 * adaptiveScale.value * props.pinSize) // 增加间距10
const chipX = computed(() => pinOffset.value)
const chipY = computed(() => pinOffset.value)
const pinsPerSide = props.pinCount / 4
const pinFontSize = computed(() => Math.max(6, 8 * adaptiveScale.value * props.pinSize))
const chipModelFontSize = computed(() => Math.min(16 * adaptiveScale.value * props.pinSize, chipSize.value / 12))

// SVG总尺寸计算（侧边图例布局）
const svgWidth = computed(() => {
  const pinHeight = 25 * adaptiveScale.value * props.pinSize
  // 为左侧引脚预留额外空间
  const chipAreaWidth = chipSize.value + pinOffset.value * 2 + chipAreaMargin * 2 + pinHeight
  const legendWidth = props.showLegend && legendItems.value.length > 0
    ? legendToChipSpacing.value + legendBoxWidth.value + 20
    : 0
  return chipAreaWidth + legendWidth
})

const svgHeight = computed(() => {
  // 计算引脚高度
  const pinHeight = 25 * adaptiveScale.value * props.pinSize

  // 现在chipAreaY已经包含了顶部引脚的空间(chipAreaMargin + pinHeight)
  // 内容高度：chipY(pinOffset) + 芯片高度 + pinOffset + 底部引脚高度
  // 总高度：chipAreaY + 内容高度 + 下边距(chipAreaMargin)
  const contentHeight = pinOffset.value + chipSize.value + pinOffset.value + pinHeight
  const totalHeight = chipAreaY.value + contentHeight + chipAreaMargin

  const legendHeight = props.showLegend && legendItems.value.length > 0
    ? legendBoxHeight.value + 40
    : 0

  return Math.max(totalHeight, legendHeight)
})

// SVG样式计算
const svgStyle = computed(() => {
  return {
    width: '100%',
    height: '100%',
    maxWidth: '100%',
    maxHeight: '100%'
  }
})

// 引脚位置计算（自适应缩放）
const pinPositions = computed(() => {
  const step = chipSize.value / (pinsPerSide + 1) // 引脚间距
  const pins = []
  const pinWidth = 10 * adaptiveScale.value * props.pinSize
  const pinHeight = 25 * adaptiveScale.value * props.pinSize

  for (let i = 0; i < pinsPerSide; i++) {
    const offset = step * (i + 1)

    // 顶部引脚
    pins.push({
      x: chipX.value + offset,
      y: chipY.value - pinOffset.value,
      rectX: -pinWidth / 2,
      rectY: -pinHeight,
      rectW: pinWidth,
      rectH: pinHeight,
      label: `PIN${i + 1}`,
    })

    // 右侧引脚
    pins.push({
      x: chipX.value + chipSize.value + pinOffset.value,
      y: chipY.value + offset,
      rectX: 0,
      rectY: -pinWidth / 2,
      rectW: pinHeight,
      rectH: pinWidth,
      label: `PIN${pinsPerSide + i + 1}`,
    })

    // 底部引脚
    pins.push({
      x: chipX.value + chipSize.value - offset,
      y: chipY.value + chipSize.value + pinOffset.value,
      rectX: -pinWidth / 2,
      rectY: 0,
      rectW: pinWidth,
      rectH: pinHeight,
      label: `PIN${2 * pinsPerSide + i + 1}`,
    })

    // 左侧引脚
    pins.push({
      x: chipX.value - pinOffset.value,
      y: chipY.value + chipSize.value - offset,
      rectX: -pinHeight,
      rectY: -pinWidth / 2,
      rectW: pinHeight,
      rectH: pinWidth,
      label: `PIN${3 * pinsPerSide + i + 1}`,
    })
  }
  return pins
})
// 选中状态和辅助函数
const selectedPin = ref(null)
const emit = defineEmits(['pinClick', 'pinEdit'])

const getPinColor = (pin) => {
  // 检查是否是高亮的引脚
  const pinId = pin.label.replace('PIN', '')
  if (props.highlightedPin && pinId === props.highlightedPin.toString()) {
    return '#FF4500' // 红色高亮
  }

  // 如果是内部选中的引脚，也高亮
  if (selectedPin.value === pin.label) return '#FF4500'

  const type = props.pins.find((p) => `PIN${p.pinId}` === pin.label)?.pinType || 'DEFAULT'
  return pinColors.value[type] || pinColors.value.DEFAULT
}

const getPinNumber = (pin) => {
  const pinData = props.pins.find((p) => `PIN${p.pinId}` === pin.label)
  return pinData ? pinData.pinId : '未知'
}

const onPinClick = (pin) => {
  const pinId = pin.label.replace('PIN', '')
  const pinData = props.pins.find((p) => p.pinId.toString() === pinId)

  if (pinData) {
    // 设置选中状态
    selectedPin.value = pin.label

    // 触发编辑事件，传递完整的引脚数据
    emit('pinEdit', {
      pinId: pinData.pinId,
      pinName: pinData.pinName || `Pin${pinData.pinId}`,
      pinType: pinData.pinType || 'GPIO',
      desc: pinData.desc || '',
      status: pinData.status || '可用',
      electricalType: pinData.electricalType || 'TTL'
    })
  } else {
    // 如果没有找到引脚数据，创建默认数据
    selectedPin.value = pin.label
    emit('pinEdit', {
      pinId: parseInt(pinId),
      pinName: `Pin${pinId}`,
      pinType: 'GPIO',
      desc: '',
      status: '可用',
      electricalType: 'TTL'
    })
  }
}

// 容器尺寸检测
const updateContainerSize = () => {
  if (containerRef.value) {
    const rect = containerRef.value.getBoundingClientRect()
    containerSize.value = {
      width: rect.width || 800,
      height: rect.height || 600
    }
  }
}

// 生命周期钩子
onMounted(async () => {
  await nextTick()
  updateContainerSize()

  // 设置ResizeObserver监听容器尺寸变化
  if (containerRef.value && window.ResizeObserver) {
    resizeObserver.value = new ResizeObserver(() => {
      updateContainerSize()
    })
    resizeObserver.value.observe(containerRef.value)
  }

  // 监听窗口尺寸变化作为备选方案
  window.addEventListener('resize', updateContainerSize)
})

onUnmounted(() => {
  if (resizeObserver.value) {
    resizeObserver.value.disconnect()
  }
  window.removeEventListener('resize', updateContainerSize)
})

watch(() => props.pins, () => {
  selectedPin.value = null
}, { deep: true })
</script>

<style scoped>
.pin-diagram-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: auto; /* 自适应高度 */
  min-height: 300px; /* 增加最小高度，确保芯片图完整显示 */
  max-height: 500px; /* 增加最大高度 */
  padding: 0; /* 移除内部padding，使用外部容器的padding */
  box-sizing: border-box;
  overflow: visible; /* 改为可见，确保引脚完整显示 */
  position: relative;
  z-index: 1; /* 确保在标题之下 */
}

.pin-group {
  cursor: pointer;
  /* 移除悬停闪烁效果 */
}

/* SVG自适应样式 */
svg {
  display: block;
  overflow: visible;
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
}
</style>